#!/bin/bash

# Vibe Typer Backend Setup Script
# This script sets up the backend service for the Vibe Typer application

set -e

echo "🚀 Setting up Vibe Typer Backend..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Navigate to backend directory
cd backend

# Install dependencies
echo "📦 Installing backend dependencies..."
npm install

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo ""
    echo "⚠️  IMPORTANT: Please edit backend/.env with your configuration:"
    echo "   - OPENAI_API_KEY: Your OpenAI API key"
    echo "   - SUPABASE_URL: Your Supabase project URL"
    echo "   - SUPABASE_ANON_KEY: Your Supabase anon key"
    echo "   - SUPABASE_SERVICE_ROLE_KEY: Your Supabase service role key"
    echo ""
else
    echo "✅ .env file already exists"
fi

# Build the TypeScript code
echo "🔨 Building TypeScript code..."
npm run build

echo ""
echo "✅ Backend setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit backend/.env with your configuration"
echo "2. Set up your Supabase database using backend/supabase-schema.sql"
echo "3. Start the backend with: cd backend && npm run dev"
echo ""
echo "For more information, see backend/README.md"
