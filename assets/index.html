<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vibe Typer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f4; /* stone-100 */
            color: #57534e; /* stone-600 */
            height: 100vh;
            overflow: hidden;
        }

        .container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: #f5f5f4; /* stone-100 */
        }

        .header {
            padding: 20px;
            border-bottom: 1px solid #d6d3d1; /* stone-300 */
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header .brand {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .header h1 {
            color: #1c1917; /* stone-900 */
            font-size: 28px;
            font-weight: 700;
            margin: 0;
        }

        .header .logo {
            width: 32px;
            height: 32px;
            background: #e9d5ff; /* purple-200 */
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7c3aed; /* purple-700 */
            font-size: 18px;
        }

        .header .tagline {
            color: #78716c; /* stone-500 */
            font-size: 14px;
            font-weight: 400;
            margin-top: 4px;
        }

        /* Header Authentication */
        .header-auth {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header-auth-unauthenticated {
            display: flex;
            align-items: center;
        }

        .header-auth-authenticated {
            display: flex;
            align-items: center;
            position: relative;
        }

        /* Compact user menu button */
        .header-user-menu {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 6px 10px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 20px;
            border: 1px solid #e7e5e4; /* stone-200 */
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 13px;
        }

        .header-user-menu:hover {
            background: rgba(255, 255, 255, 1);
            border-color: #d6d3d1; /* stone-300 */
        }

        .header-user-avatar {
            width: 24px;
            height: 24px;
            background: #e9d5ff; /* purple-200 */
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7c3aed; /* purple-700 */
            font-weight: 600;
            font-size: 11px;
        }

        .header-user-email {
            color: #44403c; /* stone-700 */
            font-size: 13px;
            font-weight: 500;
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .header-menu-arrow {
            color: #78716c; /* stone-500 */
            font-size: 12px;
            margin-left: 2px;
        }

        /* Dropdown menu */
        .header-user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e7e5e4; /* stone-200 */
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            padding: 8px 0;
            min-width: 180px;
            z-index: 1000;
            margin-top: 4px;
        }

        .header-user-dropdown.hidden {
            display: none;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            color: #44403c; /* stone-700 */
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.15s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
        }

        .dropdown-item:hover {
            background: #f5f5f4; /* stone-100 */
        }

        .dropdown-item-icon {
            font-size: 14px;
            width: 16px;
            text-align: center;
        }

        .dropdown-divider {
            height: 1px;
            background: #e7e5e4; /* stone-200 */
            margin: 4px 0;
        }

        .btn-header {
            padding: 6px 14px;
            font-size: 13px;
            font-weight: 600;
            border-radius: 18px;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-header-primary {
            background: #e9d5ff; /* purple-200 */
            color: #6b21a8; /* purple-800 */
            border: 1px solid #d8b4fe; /* purple-300 */
        }

        .btn-header-primary:hover {
            background: #ddd6fe; /* purple-300 */
            transform: translateY(-1px);
        }

        .btn-header-secondary {
            background: white;
            color: #57534e; /* stone-600 */
            border: 1px solid #d6d3d1; /* stone-300 */
        }

        .btn-header-secondary:hover {
            background: #fafaf9; /* stone-50 */
            border-color: #a8a29e; /* stone-400 */
        }

        .main-content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
            background: #f5f5f4; /* stone-100 */
        }

        .status-section {
            text-align: center;
            margin-bottom: 32px;
            padding: 24px;
            background: white;
            border-radius: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
            border: 1px solid #e7e5e4; /* stone-200 */
        }

        .status-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            margin-bottom: 20px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #22c55e; /* green-500 for connected */
            transition: all 0.3s ease;
        }

        .status-dot.recording {
            background: #ef4444; /* red-500 */
            animation: pulse 2s infinite;
        }

        .status-dot.disconnected {
            background: #6b7280; /* gray-500 */
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .status-text {
            color: #44403c; /* stone-700 */
            font-size: 18px;
            font-weight: 500;
        }

        .controls {
            display: flex;
            gap: 16px;
            justify-content: center;
            margin-bottom: 32px;
        }

        .btn {
            padding: 14px 28px;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 140px;
            justify-content: center;
        }

        .btn-primary {
            background: #e9d5ff; /* purple-200 */
            color: #6b21a8; /* purple-800 */
            border: 1px solid #d8b4fe; /* purple-300 */
        }

        .btn-primary:hover {
            background: #ddd6fe; /* purple-300 */
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15);
        }

        .btn-primary.recording {
            background: #fee2e2; /* red-100 */
            color: #dc2626; /* red-600 */
            border: 1px solid #fecaca; /* red-200 */
        }

        .btn-primary.recording:hover {
            background: #fecaca; /* red-200 */
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.15);
        }

        .btn-secondary {
            background: white;
            color: #57534e; /* stone-600 */
            border: 1px solid #d6d3d1; /* stone-300 */
        }

        .btn-secondary:hover {
            background: #fafaf9; /* stone-50 */
            border-color: #a8a29e; /* stone-400 */
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .config-section {
            background: white;
            border-radius: 20px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
            border: 1px solid #e7e5e4; /* stone-200 */
            transition: all 0.3s ease;
        }

        .config-section:hover {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.06);
        }

        .config-section h3 {
            color: #1c1917; /* stone-900 */
            margin-bottom: 20px;
            font-size: 20px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .config-section h3::before {
            content: '';
            width: 4px;
            height: 20px;
            background: #a855f7; /* purple-600 */
            border-radius: 2px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #44403c; /* stone-700 */
            font-weight: 600;
            font-size: 15px;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d6d3d1; /* stone-300 */
            border-radius: 10px;
            font-size: 15px;
            background: white;
            color: #1c1917; /* stone-900 */
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #a855f7; /* purple-600 */
            box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1);
        }

        .hotkey-display {
            background: #fafaf9; /* stone-50 */
            border: 1px solid #d6d3d1; /* stone-300 */
            border-radius: 10px;
            padding: 16px;
            text-align: center;
            font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
            font-weight: 600;
            color: #44403c; /* stone-700 */
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            min-height: 24px;
        }

        .hotkey-display:hover {
            background: white;
            border-color: #a8a29e; /* stone-400 */
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .hotkey-display.capturing {
            background: #ede9fe; /* purple-100 */
            border-color: #a855f7; /* purple-600 */
            box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1);
            animation: glow 2s infinite;
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1); }
            50% { box-shadow: 0 0 0 6px rgba(168, 85, 247, 0.15); }
        }

        .hotkey-preview {
            color: #a855f7; /* purple-600 */
            font-weight: 700;
        }

        .hotkey-suggestions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
            flex-wrap: wrap;
        }

        .hotkey-suggestion {
            background: #e9d5ff; /* purple-200 */
            color: #7c3aed; /* purple-700 */
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid #d8b4fe; /* purple-300 */
        }

        .hotkey-suggestion:hover {
            background: #ddd6fe; /* purple-300 */
            transform: translateY(-1px);
        }

        .hotkey-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }

        .hotkey-actions .btn {
            padding: 8px 16px;
            font-size: 14px;
            min-width: auto;
        }

        .validation-message {
            margin-top: 8px;
            padding: 10px 14px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
        }

        .validation-message.error {
            background: #fee2e2; /* red-100 */
            color: #dc2626; /* red-600 */
            border: 1px solid #fecaca; /* red-200 */
        }

        .validation-message.success {
            background: #dcfce7; /* green-100 */
            color: #166534; /* green-800 */
            border: 1px solid #bbf7d0; /* green-200 */
        }

        .validation-message.warning {
            background: #fef3c7; /* yellow-100 */
            color: #92400e; /* yellow-800 */
            border: 1px solid #fde68a; /* yellow-200 */
        }

        .auth-section {
            background: white;
            border-radius: 20px;
            padding: 32px;
            margin-bottom: 24px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.06);
            border: 1px solid #e7e5e4; /* stone-200 */
        }

        .auth-title {
            text-align: center;
            color: #1c1917; /* stone-900 */
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 24px;
        }

        .auth-form {
            max-width: 320px;
            margin: 0 auto;
        }

        .auth-form .btn {
            width: 100%;
            margin-top: 16px;
        }

        .auth-links {
            text-align: center;
            margin-top: 20px;
        }

        .auth-links button {
            background: none;
            border: none;
            color: #a855f7; /* purple-600 */
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin: 0 8px;
            padding: 4px 0;
            text-decoration: underline;
            transition: color 0.2s ease;
        }

        .auth-links button:hover {
            color: #7c3aed; /* purple-700 */
        }

        .user-info {
            text-align: center;
            padding: 20px;
            background: #dcfce7; /* green-100 */
            border: 1px solid #bbf7d0; /* green-200 */
            border-radius: 12px;
            margin-bottom: 20px;
        }

        .user-info h3 {
            color: #166534; /* green-800 */
            margin-bottom: 8px;
            font-size: 18px;
        }

        .user-info p {
            color: #15803d; /* green-700 */
            margin-bottom: 16px;
        }

        .usage-button {
            background: #e9d5ff; /* purple-200 */
            color: #6b21a8; /* purple-800 */
            border: 1px solid #d8b4fe; /* purple-300 */
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-right: 12px;
        }

        .usage-button:hover {
            background: #ddd6fe; /* purple-300 */
        }

        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(4px);
        }

        .modal {
            background: white;
            border-radius: 20px;
            padding: 32px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
            border: 1px solid #e7e5e4; /* stone-200 */
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e7e5e4; /* stone-200 */
        }

        .modal-title {
            font-size: 24px;
            font-weight: 700;
            color: #1c1917; /* stone-900 */
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            color: #78716c; /* stone-500 */
            font-size: 24px;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.2s;
        }

        .modal-close:hover {
            color: #1c1917; /* stone-900 */
            background: #f5f5f4; /* stone-100 */
        }

        .usage-stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 16px;
            margin-bottom: 24px;
        }

        .usage-stat-card {
            background: #fafaf9; /* stone-50 */
            border: 1px solid #e7e5e4; /* stone-200 */
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }

        .usage-stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #a855f7; /* purple-600 */
            margin-bottom: 6px;
        }

        .usage-stat-label {
            font-size: 13px;
            color: #78716c; /* stone-500 */
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 600;
        }

        .usage-breakdown {
            margin-top: 24px;
        }

        .usage-breakdown-title {
            font-size: 18px;
            font-weight: 700;
            color: #1c1917; /* stone-900 */
            margin-bottom: 16px;
        }

        .usage-breakdown-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e7e5e4; /* stone-200 */
        }

        .usage-breakdown-item:last-child {
            border-bottom: none;
        }

        .usage-breakdown-label {
            color: #44403c; /* stone-700 */
            font-size: 15px;
            font-weight: 500;
        }

        .usage-breakdown-value {
            color: #a855f7; /* purple-600 */
            font-weight: 700;
            font-size: 15px;
        }

        .usage-period-info {
            background: #ede9fe; /* purple-100 */
            border: 1px solid #d8b4fe; /* purple-300 */
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 24px;
            text-align: center;
        }

        .usage-period-text {
            color: #7c3aed; /* purple-700 */
            font-weight: 600;
            font-size: 15px;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #e7e5e4; /* stone-200 */
            border-radius: 50%;
            border-top-color: #a855f7; /* purple-600 */
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f5f5f4; /* stone-100 */
        }

        ::-webkit-scrollbar-thumb {
            background: #d6d3d1; /* stone-300 */
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8a29e; /* stone-400 */
        }

                 .transcription-area {
             background: white;
             border-radius: 20px;
             padding: 24px;
             margin-bottom: 24px;
             box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
             border: 1px solid #e7e5e4; /* stone-200 */
         }

         .transcription-area h3 {
             color: #1c1917; /* stone-900 */
             margin-bottom: 20px;
             font-size: 20px;
             font-weight: 700;
             display: flex;
             align-items: center;
             gap: 10px;
         }

         .transcription-area h3::before {
             content: '💬';
             font-size: 18px;
         }

         .transcription-text {
             background: #fafaf9; /* stone-50 */
             border: 1px solid #e7e5e4; /* stone-200 */
             border-radius: 12px;
             padding: 20px;
             min-height: 120px;
             font-size: 15px;
             line-height: 1.6;
             color: #44403c; /* stone-700 */
             font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
             white-space: pre-wrap;
             word-wrap: break-word;
         }

         .transcription-text.placeholder {
             color: #a8a29e; /* stone-400 */
             font-style: italic;
             white-space: normal; /* Override pre-wrap for placeholder */
         }

         .footer {
             padding: 20px 24px;
             border-top: 1px solid #e7e5e4; /* stone-200 */
             text-align: center;
             font-size: 14px;
             color: #78716c; /* stone-500 */
             background: white;
             font-weight: 500;
         }



        /* Update auth section for when accessed via settings */
        .auth-section {
            background: white;
            border-radius: 20px;
            padding: 32px;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
            border: 1px solid #e7e5e4; /* stone-200 */
            display: none; /* Hidden by default, shown via settings */
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }
            
            .controls {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
            }
            
            .usage-stats-grid {
                grid-template-columns: 1fr;
            }
            
            .modal {
                margin: 16px;
                padding: 24px;
            }

            .header {
                flex-direction: column;
                gap: 16px;
                text-align: center;
            }

            .header-auth-authenticated {
                flex-direction: column;
                gap: 8px;
            }

            .header-auth-authenticated .btn-header {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="brand">
                <div class="logo">🎤</div>
                <div>
                    <h1>Vibe Typer</h1>
                    <div class="tagline">Speak naturally, type instantly</div>
                </div>
            </div>
            
            <!-- Header Authentication -->
            <div class="header-auth">
                <!-- Unauthenticated State -->
                <div id="headerAuthUnauthenticated" class="header-auth-unauthenticated">
                    <button id="headerSignInBtn" class="btn-header btn-header-primary">Sign In</button>
                </div>

                <!-- Authenticated State -->
                <div id="headerAuthAuthenticated" class="header-auth-authenticated hidden">
                    <div class="header-user-menu" id="headerUserMenu">
                        <div class="header-user-avatar">U</div>
                        <div class="header-user-email" id="headerUserEmail"><EMAIL></div>
                        <span class="header-menu-arrow">▼</span>
                    </div>
                    <div class="header-user-dropdown hidden" id="headerUserDropdown">
                        <button class="dropdown-item" id="headerUsageBtn">
                            <span class="dropdown-item-icon">📊</span> Usage
                        </button>
                        <button class="dropdown-item" id="headerSettingsBtn">
                            <span class="dropdown-item-icon">⚙️</span> Settings
                        </button>
                        <div class="dropdown-divider"></div>
                        <button class="dropdown-item" id="headerSignOutBtn">
                            <span class="dropdown-item-icon">🚪</span> Sign Out
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Status Section -->
            <div class="status-section">
                <div class="status-indicator">
                    <div id="statusDot" class="status-dot"></div>
                    <span id="statusText" class="status-text">Ready to record</span>
                </div>
                
                <!-- Controls -->
                <div class="controls">
                    <button id="recordBtn" class="btn btn-primary">
                        <span>🎤 Start Recording</span>
                    </button>
                    <button id="settingsBtn" class="btn btn-secondary">
                        <span>⚙️ Settings</span>
                    </button>
                </div>
            </div>

            <!-- Account Section (Settings) - Only shows when authenticated -->
            <div class="auth-section hidden" id="authSection">
                <h3 class="auth-title">Account</h3>

                <!-- User Info (when signed in) -->
                <div id="userInfo" class="user-info">
                    <h3>Signed in as:</h3>
                    <p id="userEmail"></p>
                    <div style="display: flex; gap: 12px; justify-content: center;">
                        <button class="usage-button" id="viewUsageBtn">View Usage</button>
                        <button class="btn btn-secondary" id="signOutBtn">Sign Out</button>
                    </div>
                </div>
            </div>

            <!-- Configuration Section -->
            <div class="config-section" id="configSection">
                <h3>Configuration</h3>

                <div class="form-group">
                    <label for="language">Language:</label>
                    <select id="language">
                        <option value="en">English</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                        <option value="de">German</option>
                        <option value="zh">Chinese</option>
                        <option value="ja">Japanese</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>Global Hotkey:</label>
                    <div class="hotkey-display" id="hotkeyDisplay">Ctrl/Cmd + Shift + Space</div>
                    <div class="validation-message" id="hotkeyValidation"></div>
                    <div style="font-size: 13px; color: #78716c; margin-top: 8px; line-height: 1.4;">
                        Click to record a new hotkey combination. Must include at least one modifier key (Ctrl, Shift, Alt).
                        <br>Avoid system shortcuts like Ctrl+C, Ctrl+V, etc.
                    </div>
                    <div class="hotkey-suggestions" id="hotkeysuggestions">
                        <div style="font-size: 11px; color: #78716c; margin-bottom: 5px;">Quick suggestions:</div>
                    </div>
                    <div class="hotkey-actions">
                        <button class="btn btn-secondary" id="resetHotkeyBtn">Reset to Default</button>
                    </div>
                </div>

                <button class="btn btn-primary" id="saveConfigBtn">Save Configuration</button>
            </div>

            <!-- AI Commands Help -->
            <div class="config-section">
                <h3>AI Voice Commands</h3>
                <div style="font-size: 12px; color: #78716c; line-height: 1.4;">
                    <strong>Write Mode:</strong> "Write an answer to the message in my clipboard, and tell them politely that their offer is not as compelling as competing offers"<br><br>
                    <strong>Answer Mode:</strong> "Answer the question: How tall is the Eiffel tower?"<br><br>
                    <strong>Rewrite Mode:</strong> "Rewrite this to Portuguese" or "Rewrite this to bullet points"<br><br>
                    <strong>Reply Mode:</strong> "Reply that we don't offer these services, in a polite way"
                </div>
            </div>

            <!-- Transcription Area -->
            <div class="transcription-area">
                <h3>Last Transcription / AI Response</h3>
                <div class="transcription-text placeholder" id="transcriptionText">Transcribed text or AI responses will appear here...</div>
            </div>
        </div>

        <div class="footer">
            Press your configured hotkey to toggle recording • Text will be automatically inserted
        </div>
    </div>

    <!-- Authentication Modal -->
    <div id="authModal" class="modal-overlay hidden">
        <div class="modal">
            <div class="modal-header">
                <h3 id="authModalTitle" class="modal-title">Sign In</h3>
                <button class="modal-close" id="authModalClose">&times;</button>
            </div>
                <!-- Sign In Form -->
                <div id="signInForm" class="auth-form">
                    <div class="form-group">
                        <label for="signInEmail">Email:</label>
                        <input type="email" id="signInEmail" placeholder="<EMAIL>">
                    </div>

                    <div class="form-group">
                        <label for="signInPassword">Password:</label>
                        <input type="password" id="signInPassword" placeholder="Password">
                    </div>

                    <div class="form-group">
                        <button class="btn btn-primary" id="signInBtn">Sign In</button>
                        <button class="btn btn-secondary" id="showSignUpBtn">Create Account</button>
                    </div>

                    <div class="auth-links">
                        <button id="forgotPasswordBtn">Forgot Password?</button>
                    </div>
                </div>

                <!-- Sign Up Form -->
                <div id="signUpForm" class="hidden auth-form">
                    <div class="form-group">
                        <label for="signUpEmail">Email:</label>
                        <input type="email" id="signUpEmail" placeholder="<EMAIL>">
                    </div>

                    <div class="form-group">
                        <label for="signUpPassword">Password:</label>
                        <input type="password" id="signUpPassword" placeholder="Password (min 6 characters)">
                    </div>

                    <div class="form-group">
                        <label for="signUpPasswordConfirm">Confirm Password:</label>
                        <input type="password" id="signUpPasswordConfirm" placeholder="Confirm Password">
                    </div>

                    <div class="form-group">
                        <button class="btn btn-primary" id="signUpBtn">Create Account</button>
                        <button class="btn btn-secondary" id="showSignInBtn">Back to Sign In</button>
                    </div>
                </div>

                <!-- Auth Messages -->
                <div id="authError" class="validation-message error hidden"></div>
                <div id="authSuccess" class="validation-message success hidden"></div>
        </div>
    </div>

    <!-- Usage Statistics Modal -->
    <div id="usageModal" class="modal-overlay hidden" role="dialog" aria-labelledby="usageModalTitle" aria-modal="true">
        <div class="modal">
            <div class="modal-header">
                <h2 id="usageModalTitle" class="modal-title">Usage Statistics</h2>
                <button class="modal-close" id="usageModalClose" aria-label="Close usage statistics">&times;</button>
            </div>

            <div class="usage-period-info">
                <div class="usage-period-text" id="usagePeriodText">Last 30 days</div>
            </div>

            <div class="usage-stats-grid">
                <div class="usage-stat-card">
                    <div class="usage-stat-value" id="totalRequestsValue">-</div>
                    <div class="usage-stat-label">Total Requests</div>
                </div>
                <div class="usage-stat-card">
                    <div class="usage-stat-value" id="totalTokensValue">-</div>
                    <div class="usage-stat-label">Total Tokens</div>
                </div>
                <div class="usage-stat-card">
                    <div class="usage-stat-value" id="totalAudioValue">-</div>
                    <div class="usage-stat-label">Audio Duration (sec)</div>
                </div>

            </div>

            <div class="usage-breakdown">
                <div class="usage-breakdown-title">Breakdown by Service</div>
                <div id="usageBreakdownList">
                    <!-- Breakdown items will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
