const { ipc<PERSON><PERSON><PERSON> } = require('electron');

class RendererApp {
    constructor() {
        this.isRecording = false;
        this.config = null;
        this.currentUser = null;
        this.isAuthenticated = false;

        // Web Audio API properties
        this.audioContext = null;
        this.mediaStream = null;
        this.scriptProcessor = null;
        this.microphone = null;

        this.initializeElements();
        this.setupEventListeners();
        this.loadConfiguration();
        this.checkAuthState();
        this.initializeTranscriptionArea();
    }

    initializeElements() {
        // Status elements
        this.statusDot = document.getElementById('statusDot');
        this.statusText = document.getElementById('statusText');
        
        // Control elements
        this.recordBtn = document.getElementById('recordBtn');
        this.settingsBtn = document.getElementById('settingsBtn');
        this.saveConfigBtn = document.getElementById('saveConfigBtn');
        
        // Authentication elements
        this.authSection = document.getElementById('authSection');
        this.authTitle = document.getElementById('authTitle');
        this.signInForm = document.getElementById('signInForm');
        this.signUpForm = document.getElementById('signUpForm');
        this.userInfo = document.getElementById('userInfo');

        // Header authentication elements (always visible)
        this.headerAuthUnauthenticated = document.getElementById('headerAuthUnauthenticated');
        this.headerAuthAuthenticated = document.getElementById('headerAuthAuthenticated');
        this.headerUserEmail = document.getElementById('headerUserEmail');
        this.headerUserMenu = document.getElementById('headerUserMenu');
        this.headerUserDropdown = document.getElementById('headerUserDropdown');
        this.headerSignInBtn = document.getElementById('headerSignInBtn');
        this.headerUsageBtn = document.getElementById('headerUsageBtn');
        this.headerSettingsBtn = document.getElementById('headerSettingsBtn');
        this.headerSignOutBtn = document.getElementById('headerSignOutBtn');

        // Authentication modal elements
        this.authModal = document.getElementById('authModal');
        this.authModalTitle = document.getElementById('authModalTitle');
        this.authModalClose = document.getElementById('authModalClose');

        // Sign in form elements
        this.signInEmail = document.getElementById('signInEmail');
        this.signInPassword = document.getElementById('signInPassword');
        this.signInBtn = document.getElementById('signInBtn');
        this.showSignUpBtn = document.getElementById('showSignUpBtn');
        this.forgotPasswordBtn = document.getElementById('forgotPasswordBtn');

        // Sign up form elements
        this.signUpEmail = document.getElementById('signUpEmail');
        this.signUpPassword = document.getElementById('signUpPassword');
        this.signUpPasswordConfirm = document.getElementById('signUpPasswordConfirm');
        this.signUpBtn = document.getElementById('signUpBtn');
        this.showSignInBtn = document.getElementById('showSignInBtn');

        // User info elements
        this.userEmail = document.getElementById('userEmail');
        this.signOutBtn = document.getElementById('signOutBtn');
        this.viewUsageBtn = document.getElementById('viewUsageBtn');

        // Configuration elements
        this.configSection = document.getElementById('configSection');
        this.languageSelect = document.getElementById('language');
        this.hotkeyDisplay = document.getElementById('hotkeyDisplay');
        this.hotkeyValidation = document.getElementById('hotkeyValidation');
        this.hotkeySuggestions = document.getElementById('hotkeysuggestions');
        this.resetHotkeyBtn = document.getElementById('resetHotkeyBtn');

        // Transcription elements
        this.transcriptionText = document.getElementById('transcriptionText');

        // Error/success elements
        this.authError = document.getElementById('authError');
        this.authSuccess = document.getElementById('authSuccess');

        // Usage modal elements
        this.usageModal = document.getElementById('usageModal');
        this.usageModalClose = document.getElementById('usageModalClose');
        this.usagePeriodText = document.getElementById('usagePeriodText');
        this.totalRequestsValue = document.getElementById('totalRequestsValue');
        this.totalTokensValue = document.getElementById('totalTokensValue');
        this.totalAudioValue = document.getElementById('totalAudioValue');

        this.usageBreakdownList = document.getElementById('usageBreakdownList');

        // Hotkey configuration state
        this.isCapturingHotkey = false;
        this.capturedKeys = new Set();
        this.hotkeyTimeout = null;
        this.supportedKeys = null;
    }

    initializeTranscriptionArea() {
        // Ensure transcription area starts with placeholder styling
        this.transcriptionText.classList.add('placeholder');
    }

    setupEventListeners() {
        // Button event listeners
        this.recordBtn.addEventListener('click', () => this.toggleRecording());
        this.settingsBtn.addEventListener('click', () => this.toggleSettings());
        this.saveConfigBtn.addEventListener('click', () => this.saveConfiguration());

        // Authentication event listeners
        this.signInBtn.addEventListener('click', () => this.signIn());
        this.signUpBtn.addEventListener('click', () => this.signUp());
        this.signOutBtn.addEventListener('click', () => this.signOut());
        this.showSignUpBtn.addEventListener('click', () => this.showSignUpForm());
        this.showSignInBtn.addEventListener('click', () => this.showSignInForm());
        this.forgotPasswordBtn.addEventListener('click', () => this.resetPassword());
        this.viewUsageBtn.addEventListener('click', () => this.showUsageStats());

        // Header authentication event listeners (always visible)
        if (this.headerSignInBtn) {
            this.headerSignInBtn.addEventListener('click', () => this.showAuthModal('signin'));
        } else {
            console.error('headerSignInBtn element not found');
        }
        
        if (this.headerUserMenu) {
            this.headerUserMenu.addEventListener('click', () => this.toggleUserDropdown());
        }
        
        if (this.headerUsageBtn) {
            this.headerUsageBtn.addEventListener('click', () => this.showUsageStats());
        }
        
        if (this.headerSettingsBtn) {
            this.headerSettingsBtn.addEventListener('click', () => this.toggleSettings());
        }
        
        if (this.headerSignOutBtn) {
            this.headerSignOutBtn.addEventListener('click', () => this.signOut());
        }

        // Authentication modal event listeners
        if (this.authModalClose) {
            this.authModalClose.addEventListener('click', () => this.hideAuthModal());
        }
        
        // Close auth modal when clicking outside
        if (this.authModal) {
            this.authModal.addEventListener('click', (e) => {
                if (e.target === this.authModal) {
                    this.hideAuthModal();
                }
            });
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!this.headerAuthAuthenticated.contains(e.target)) {
                this.closeUserDropdown();
            }
        });

        // Usage modal event listeners
        this.usageModalClose.addEventListener('click', () => this.hideUsageModal());
        this.usageModal.addEventListener('click', (e) => {
            if (e.target === this.usageModal) {
                this.hideUsageModal();
            }
        });

        // Enter key handling for auth forms
        this.signInEmail.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.signIn();
        });
        this.signInPassword.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.signIn();
        });
        this.signUpPasswordConfirm.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.signUp();
        });
        
        // Hotkey configuration
        this.hotkeyDisplay.addEventListener('click', () => this.startHotkeyCapture());
        this.resetHotkeyBtn.addEventListener('click', () => this.resetHotkeyToDefault());
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        document.addEventListener('keyup', (e) => this.handleKeyUp(e));
        
        // IPC event listeners from main process
        ipcRenderer.on('recording-started', () => this.onRecordingStarted());
        ipcRenderer.on('recording-stopped', () => this.onRecordingStopped());
        ipcRenderer.on('transcription-complete', (event, text) => this.onTranscriptionComplete(text));
        ipcRenderer.on('ai-response-complete', (event, data) => this.onAIResponseComplete(data));
        ipcRenderer.on('hotkey-fallback-used', (event, data) => this.onHotkeyFallbackUsed(data));
        ipcRenderer.on('hotkey-registration-failed', (event, data) => this.onHotkeyRegistrationFailed(data));
        ipcRenderer.on('auth-state-changed', (event, data) => this.onAuthStateChanged(data));

        // Web Audio API IPC listeners
        ipcRenderer.on('start-audio-recording', (event, options) => this.startWebAudioRecording(options));
        ipcRenderer.on('stop-audio-recording', () => this.stopWebAudioRecording());
        
        // Window focus events
        window.addEventListener('focus', () => this.onWindowFocus());
        window.addEventListener('blur', () => this.onWindowBlur());

        // Keyboard events for modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                if (!this.usageModal.classList.contains('hidden')) {
                    this.hideUsageModal();
                } else if (!this.authModal.classList.contains('hidden')) {
                    this.hideAuthModal();
                }
            }
        });
    }

    async loadConfiguration() {
        try {
            this.config = await ipcRenderer.invoke('get-config');
            this.supportedKeys = await ipcRenderer.invoke('get-supported-keys');
            this.updateUIFromConfig();
            this.setupHotkeySuggestions();
        } catch (error) {
            console.error('Failed to load configuration:', error);
            this.showError('Failed to load configuration');
        }
    }

    // Authentication methods
    async checkAuthState() {
        try {
            const user = await ipcRenderer.invoke('auth-get-current-user');
            if (user) {
                this.currentUser = user;
                this.isAuthenticated = true;
                this.updateAuthUI();
            } else {
                this.currentUser = null;
                this.isAuthenticated = false;
                this.updateAuthUI();
            }
        } catch (error) {
            console.error('Failed to check auth state:', error);
            this.currentUser = null;
            this.isAuthenticated = false;
            this.updateAuthUI();
        }
    }

    async signIn() {
        const email = this.signInEmail.value.trim();
        const password = this.signInPassword.value;

        if (!email || !password) {
            this.showAuthError('Please enter both email and password');
            return;
        }

        try {
            this.signInBtn.disabled = true;
            this.signInBtn.textContent = 'Signing in...';

            const result = await ipcRenderer.invoke('auth-sign-in', email, password);

            if (result.success) {
                this.currentUser = result.data.user;
                this.isAuthenticated = true;
                this.showAuthSuccess('Signed in successfully!');
                this.clearAuthForms();
                this.updateAuthUI();
            } else {
                if (!this.isAuthenticated) {
                    this.showAuthError(result.error || 'Sign in failed');
                }
            }
        } catch (error) {
            console.error('Sign in error:', error);
            if (!this.isAuthenticated) {
                this.showAuthError('Sign in failed. Please try again.');
            }
        } finally {
            this.signInBtn.disabled = false;
            this.signInBtn.textContent = 'Sign In';
        }
    }

    async signUp() {
        const email = this.signUpEmail.value.trim();
        const password = this.signUpPassword.value;
        const confirmPassword = this.signUpPasswordConfirm.value;

        if (!email || !password || !confirmPassword) {
            this.showAuthError('Please fill in all fields');
            return;
        }

        if (password !== confirmPassword) {
            this.showAuthError('Passwords do not match');
            return;
        }

        if (password.length < 6) {
            this.showAuthError('Password must be at least 6 characters long');
            return;
        }

        try {
            this.signUpBtn.disabled = true;
            this.signUpBtn.textContent = 'Creating account...';

            const result = await ipcRenderer.invoke('auth-sign-up', email, password);

            if (result.success) {
                this.showAuthSuccess('Account created successfully! Please sign in.');
                this.clearAuthForms();
                this.showSignInForm();
            } else {
                this.showAuthError(result.error || 'Account creation failed');
            }
        } catch (error) {
            console.error('Sign up error:', error);
            this.showAuthError('Account creation failed. Please try again.');
        } finally {
            this.signUpBtn.disabled = false;
            this.signUpBtn.textContent = 'Create Account';
        }
    }

    async signOut() {
        try {
            this.signOutBtn.disabled = true;
            this.signOutBtn.textContent = 'Signing out...';

            const result = await ipcRenderer.invoke('auth-sign-out');

            if (result.success) {
                this.currentUser = null;
                this.isAuthenticated = false;
                this.showAuthSuccess('Signed out successfully');
                this.clearAuthForms();
                this.updateAuthUI();
            } else {
                if (this.isAuthenticated) {
                    this.showAuthError(result.error || 'Sign out failed');
                }
            }
        } catch (error) {
            console.error('Sign out error:', error);
            if (this.isAuthenticated) {
                this.showAuthError('Sign out failed. Please try again.');
            }
        } finally {
            this.signOutBtn.disabled = false;
            this.signOutBtn.textContent = 'Sign Out';
        }
    }

    async resetPassword() {
        const email = this.signInEmail.value.trim();

        if (!email) {
            this.showAuthError('Please enter your email address first');
            return;
        }

        try {
            const result = await ipcRenderer.invoke('auth-reset-password', email);

            if (result.success) {
                this.showAuthSuccess('Password reset email sent! Check your inbox.');
            } else {
                this.showAuthError(result.error || 'Password reset failed');
            }
        } catch (error) {
            console.error('Password reset error:', error);
            this.showAuthError('Password reset failed. Please try again.');
        }
    }

    showSignInForm() {
        this.authTitle.textContent = 'Sign In';
        this.signInForm.classList.remove('hidden');
        this.signUpForm.classList.add('hidden');
        this.clearAuthMessages();
    }

    showSignUpForm() {
        this.authTitle.textContent = 'Create Account';
        this.signInForm.classList.add('hidden');
        this.signUpForm.classList.remove('hidden');
        this.clearAuthMessages();
    }

    showAuthModal(mode = 'signin') {
        console.log('showAuthModal called with mode:', mode);
        
        if (!this.authModal) {
            console.error('Auth modal not found');
            return;
        }

        // Clear any previous messages
        this.clearAuthMessages();
        
        // Set up the modal for the correct mode
        if (mode === 'signup') {
            this.showSignUpForm();
            this.authModalTitle.textContent = 'Create Account';
        } else {
            this.showSignInForm();
            this.authModalTitle.textContent = 'Sign In';
        }

        // Show the modal
        this.authModal.classList.remove('hidden');
        
        // Focus the first input field
        setTimeout(() => {
            const firstInput = this.authModal.querySelector('input');
            if (firstInput) {
                firstInput.focus();
            }
        }, 100);
    }

    hideAuthModal() {
        if (this.authModal) {
            this.authModal.classList.add('hidden');
            this.clearAuthForms();
            this.clearAuthMessages();
        }
    }

    updateAuthUI() {
        if (this.isAuthenticated && this.currentUser) {
            // Update header authentication - AUTHENTICATED
            this.headerAuthUnauthenticated.classList.add('hidden');
            this.headerAuthAuthenticated.classList.remove('hidden');
            this.headerUserEmail.textContent = this.currentUser.email;
            
            // Set user avatar to first letter of email
            const avatar = this.headerUserMenu.querySelector('.header-user-avatar');
            if (avatar) {
                avatar.textContent = this.currentUser.email.charAt(0).toUpperCase();
            }

            // Close dropdown when auth state changes
            this.closeUserDropdown();

            // Update settings auth section - show account info
            this.authSection.classList.remove('hidden');
            this.userEmail.textContent = this.currentUser.email;

            // Hide authentication modal if open
            this.hideAuthModal();

            // Update main status to ready
            this.statusText.textContent = 'Ready to record';
            this.statusDot.classList.remove('disconnected');
        } else {
            // Update header authentication - UNAUTHENTICATED  
            this.headerAuthAuthenticated.classList.add('hidden');
            this.headerAuthUnauthenticated.classList.remove('hidden');

            // Close dropdown when auth state changes
            this.closeUserDropdown();

            // Hide auth section in settings when not authenticated
            this.authSection.classList.add('hidden');

            // Update main status to show auth requirement
            this.statusText.textContent = 'Sign in required';
            this.statusDot.classList.add('disconnected');
        }
    }

    toggleUserDropdown() {
        this.headerUserDropdown.classList.toggle('hidden');
    }

    closeUserDropdown() {
        this.headerUserDropdown.classList.add('hidden');
    }

    onAuthStateChanged(data) {
        this.isAuthenticated = data.authenticated;
        this.currentUser = data.user;
        this.updateAuthUI();

        if (data.authenticated) {
            this.showAuthSuccess(`Welcome back, ${data.user.email}!`);
        }
    }

    clearAuthForms() {
        this.signInEmail.value = '';
        this.signInPassword.value = '';
        this.signUpEmail.value = '';
        this.signUpPassword.value = '';
        this.signUpPasswordConfirm.value = '';
    }

    clearAuthMessages() {
        this.authError.classList.add('hidden');
        this.authSuccess.classList.add('hidden');
    }

    showAuthError(message) {
        this.authError.textContent = message;
        this.authError.classList.remove('hidden');
        this.authSuccess.classList.add('hidden');
    }

    showAuthSuccess(message) {
        this.authSuccess.textContent = message;
        this.authSuccess.classList.remove('hidden');
        this.authError.classList.add('hidden');
    }

    async showUsageStats() {
        try {
            // Show modal with loading state
            this.showUsageModal();
            this.setUsageModalLoading(true);

            const stats = await ipcRenderer.invoke('get-usage-stats', 30);
            this.populateUsageModal(stats, 30);
            this.setUsageModalLoading(false);
        } catch (error) {
            console.error('Failed to get usage stats:', error);
            this.hideUsageModal();
            this.showAuthError('Failed to load usage statistics');
        }
    }

    showUsageModal() {
        this.usageModal.classList.remove('hidden');
        // Focus the modal for accessibility
        this.usageModal.focus();
    }

    hideUsageModal() {
        this.usageModal.classList.add('hidden');
    }

    setUsageModalLoading(isLoading) {
        if (isLoading) {
            this.totalRequestsValue.textContent = '...';
            this.totalTokensValue.textContent = '...';
            this.totalAudioValue.textContent = '...';

            this.usageBreakdownList.innerHTML = '<div class="usage-breakdown-item"><span class="usage-breakdown-label">Loading...</span><span class="usage-breakdown-value">...</span></div>';
        }
    }

    populateUsageModal(stats, days) {
        // Update period text
        this.usagePeriodText.textContent = `Last ${days} days`;

        // Update main statistics
        this.totalRequestsValue.textContent = stats.totalRequests.toLocaleString();
        this.totalTokensValue.textContent = stats.totalTokens.toLocaleString();
        this.totalAudioValue.textContent = Math.round(stats.totalAudioDuration).toLocaleString();



        // Populate breakdown by service
        this.populateUsageBreakdown(stats.requestsByEndpoint);
    }



    populateUsageBreakdown(requestsByEndpoint) {
        this.usageBreakdownList.innerHTML = '';

        // Define friendly names for endpoints
        const endpointNames = {
            'transcribe': 'Voice Transcription',
            'chat/completions': 'AI Processing',
            'process-ai': 'AI Processing'
        };

        // Sort endpoints by usage count
        const sortedEndpoints = Object.entries(requestsByEndpoint)
            .sort(([,a], [,b]) => b - a);

        if (sortedEndpoints.length === 0) {
            const noDataItem = document.createElement('div');
            noDataItem.className = 'usage-breakdown-item';
            noDataItem.innerHTML = `
                <span class="usage-breakdown-label">No usage data available</span>
                <span class="usage-breakdown-value">-</span>
            `;
            this.usageBreakdownList.appendChild(noDataItem);
            return;
        }

        sortedEndpoints.forEach(([endpoint, count]) => {
            const friendlyName = endpointNames[endpoint] || endpoint;
            const item = document.createElement('div');
            item.className = 'usage-breakdown-item';
            item.innerHTML = `
                <span class="usage-breakdown-label">${friendlyName}</span>
                <span class="usage-breakdown-value">${count.toLocaleString()} requests</span>
            `;
            this.usageBreakdownList.appendChild(item);
        });
    }

    updateUIFromConfig() {
        if (!this.config) return;

        // Update language
        this.languageSelect.value = this.config.speechSettings.language || 'en';

        // Update hotkey display
        this.updateHotkeyDisplay(this.config.hotkey || 'CommandOrControl+Shift+Space');

        // Hide settings sections by default (but auth status remains visible)
        this.configSection.style.display = 'none';
        this.authSection.style.display = 'none';
        this.settingsBtn.innerHTML = '<span>⚙️ Settings</span>';
    }

    async updateHotkeyDisplay(hotkey) {
        try {
            const displayText = await ipcRenderer.invoke('format-hotkey', hotkey);
            this.hotkeyDisplay.textContent = displayText || hotkey || 'Ctrl/Cmd + Shift + Space';
            this.hotkeyDisplay.className = 'hotkey-display';
            this.clearHotkeyValidation();
        } catch (error) {
            console.error('Failed to format hotkey:', error);
            this.hotkeyDisplay.textContent = hotkey || 'Ctrl/Cmd + Shift + Space';
        }
    }

    setupHotkeySuggestions() {
        if (!this.hotkeySuggestions) return;

        // Clear existing suggestions
        const existingSuggestions = this.hotkeySuggestions.querySelectorAll('.hotkey-suggestion');
        existingSuggestions.forEach(el => el.remove());

        // Add default suggestions
        const suggestions = [
            'CommandOrControl+Shift+Space',
            'CommandOrControl+Alt+Space',
            'F2',
            'F3',
            'Alt+Space'
        ];

        suggestions.forEach(suggestion => {
            const suggestionEl = document.createElement('div');
            suggestionEl.className = 'hotkey-suggestion';
            suggestionEl.textContent = suggestion.replace('CommandOrControl', 'Ctrl/Cmd');
            suggestionEl.addEventListener('click', () => this.applySuggestedHotkey(suggestion));
            this.hotkeySuggestions.appendChild(suggestionEl);
        });
    }

    async applySuggestedHotkey(hotkey) {
        try {
            // Test the hotkey first
            const testResult = await ipcRenderer.invoke('test-hotkey', hotkey);
            if (!testResult.available) {
                this.showHotkeyValidation(testResult.error || 'Hotkey not available', 'error');
                return;
            }

            // Apply the hotkey
            const result = await ipcRenderer.invoke('update-hotkey', hotkey);
            if (result.success) {
                this.config.hotkey = hotkey;
                this.updateHotkeyDisplay(hotkey);
                this.showHotkeyValidation('Hotkey updated successfully!', 'success');
            } else {
                this.showHotkeyValidation(result.error || 'Failed to update hotkey', 'error');
            }
        } catch (error) {
            console.error('Failed to apply suggested hotkey:', error);
            this.showHotkeyValidation('Failed to apply hotkey', 'error');
        }
    }

    startHotkeyCapture() {
        if (this.isCapturingHotkey) return;

        this.isCapturingHotkey = true;
        this.capturedKeys.clear();
        this.hotkeyDisplay.textContent = 'Press your desired key combination...';
        this.hotkeyDisplay.className = 'hotkey-display capturing';
        this.clearHotkeyValidation();

        // Auto-cancel after 10 seconds
        this.hotkeyTimeout = setTimeout(() => {
            if (this.isCapturingHotkey) {
                this.cancelHotkeyCapture();
            }
        }, 10000);
    }

    handleKeyDown(event) {
        if (!this.isCapturingHotkey) return;
        
        event.preventDefault();
        event.stopPropagation();
        
        // Add key to captured keys
        if (event.ctrlKey) this.capturedKeys.add('ctrl');
        if (event.shiftKey) this.capturedKeys.add('shift');
        if (event.altKey) this.capturedKeys.add('alt');
        if (event.metaKey) this.capturedKeys.add('cmd');
        
        // Add the main key (not modifier keys)
        if (!['Control', 'Shift', 'Alt', 'Meta'].includes(event.key)) {
            // Map special keys to their proper names
            let keyName = event.key;
            if (keyName === ' ') keyName = 'space';
            else if (keyName === 'Enter') keyName = 'return';
            else if (keyName === 'Escape') keyName = 'escape';
            else if (keyName === 'Tab') keyName = 'tab';
            else if (keyName === 'Backspace') keyName = 'backspace';
            else if (keyName === 'Delete') keyName = 'delete';
            else keyName = keyName.toLowerCase();
            
            this.capturedKeys.add(keyName);
        }
        
        this.updateHotkeyPreview();
    }

    handleKeyUp(event) {
        if (!this.isCapturingHotkey) return;
        
        event.preventDefault();
        event.stopPropagation();
        
        // If this was a modifier key release and we have a complete combination, save it
        if (['Control', 'Shift', 'Alt', 'Meta'].includes(event.key) && this.capturedKeys.size >= 2) {
            this.saveHotkey();
        }
    }

    updateHotkeyPreview() {
        const keys = Array.from(this.capturedKeys);
        const displayKeys = [];
        
        // Order modifiers first
        if (keys.includes('ctrl')) displayKeys.push('Ctrl');
        if (keys.includes('cmd')) displayKeys.push('Cmd');
        if (keys.includes('shift')) displayKeys.push('Shift');
        if (keys.includes('alt')) displayKeys.push('Alt');
        
        // Add main key
        const mainKey = keys.find(k => !['ctrl', 'cmd', 'shift', 'alt'].includes(k));
        if (mainKey) {
            // Format special keys for display
            let displayKey = mainKey;
            if (mainKey === 'space') displayKey = 'Space';
            else if (mainKey === 'return') displayKey = 'Enter';
            else if (mainKey === 'escape') displayKey = 'Esc';
            else if (mainKey === 'tab') displayKey = 'Tab';
            else if (mainKey === 'backspace') displayKey = 'Backspace';
            else if (mainKey === 'delete') displayKey = 'Delete';
            else displayKey = mainKey.toUpperCase();
            
            displayKeys.push(displayKey);
        }
        
        this.hotkeyDisplay.textContent = displayKeys.join(' + ') || 'Press keys...';
    }

    async saveHotkey() {
        const keys = Array.from(this.capturedKeys);

        // Convert to Electron format
        let electronKeys = [];

        if (keys.includes('ctrl') || keys.includes('cmd')) {
            electronKeys.push('CommandOrControl');
        }
        if (keys.includes('shift')) electronKeys.push('Shift');
        if (keys.includes('alt')) electronKeys.push('Alt');

        const mainKey = keys.find(k => !['ctrl', 'cmd', 'shift', 'alt'].includes(k));
        if (mainKey) {
            // Map to Electron accelerator format
            let electronKey = mainKey;
            if (mainKey === 'space') electronKey = 'Space';
            else if (mainKey === 'return') electronKey = 'Return';
            else if (mainKey === 'escape') electronKey = 'Escape';
            else if (mainKey === 'tab') electronKey = 'Tab';
            else if (mainKey === 'backspace') electronKey = 'Backspace';
            else if (mainKey === 'delete') electronKey = 'Delete';
            else if (mainKey.startsWith('f') && mainKey.length <= 3) electronKey = mainKey.toUpperCase();
            else electronKey = mainKey.charAt(0).toUpperCase() + mainKey.slice(1);

            electronKeys.push(electronKey);
        }

        if (electronKeys.length < 2) {
            this.showHotkeyValidation('Hotkey must include at least one modifier key', 'error');
            this.hotkeyDisplay.className = 'hotkey-display error';
            setTimeout(() => this.cancelHotkeyCapture(), 2000);
            return;
        }

        const newHotkey = electronKeys.join('+');

        try {
            // First validate the hotkey
            const validation = await ipcRenderer.invoke('validate-hotkey', newHotkey);
            if (!validation.valid) {
                this.showHotkeyValidation(validation.error, 'error');
                this.hotkeyDisplay.className = 'hotkey-display error';
                setTimeout(() => this.cancelHotkeyCapture(), 2000);
                return;
            }

            // Test if hotkey is available
            const testResult = await ipcRenderer.invoke('test-hotkey', newHotkey);
            if (!testResult.available) {
                this.showHotkeyValidation(testResult.error || 'Hotkey not available', 'error');
                this.hotkeyDisplay.className = 'hotkey-display error';
                setTimeout(() => this.cancelHotkeyCapture(), 2000);
                return;
            }

            // Apply the hotkey
            const result = await ipcRenderer.invoke('update-hotkey', newHotkey);
            if (result.success) {
                this.config.hotkey = newHotkey;
                this.hotkeyDisplay.className = 'hotkey-display success';
                this.showHotkeyValidation('Hotkey updated successfully!', 'success');
                setTimeout(() => {
                    this.updateHotkeyDisplay(newHotkey);
                    this.finishHotkeyCapture();
                }, 1500);
            } else {
                this.showHotkeyValidation(result.error || 'Failed to register hotkey', 'error');
                this.hotkeyDisplay.className = 'hotkey-display error';
                setTimeout(() => this.cancelHotkeyCapture(), 2000);
            }
        } catch (error) {
            console.error('Failed to update hotkey:', error);
            this.showHotkeyValidation('Failed to update hotkey', 'error');
            this.hotkeyDisplay.className = 'hotkey-display error';
            setTimeout(() => this.cancelHotkeyCapture(), 2000);
        }
    }

    cancelHotkeyCapture() {
        this.finishHotkeyCapture();
        this.updateHotkeyDisplay(this.config?.hotkey || 'CommandOrControl+Shift+Space');
    }

    finishHotkeyCapture() {
        this.isCapturingHotkey = false;
        this.capturedKeys.clear();
        if (this.hotkeyTimeout) {
            clearTimeout(this.hotkeyTimeout);
            this.hotkeyTimeout = null;
        }
        this.hotkeyDisplay.className = 'hotkey-display';
    }

    showHotkeyValidation(message, type = 'error') {
        if (!this.hotkeyValidation) return;

        this.hotkeyValidation.textContent = message;
        this.hotkeyValidation.className = `hotkey-validation ${type}`;
    }

    clearHotkeyValidation() {
        if (!this.hotkeyValidation) return;

        this.hotkeyValidation.textContent = '';
        this.hotkeyValidation.className = 'hotkey-validation';
    }

    onHotkeyFallbackUsed(data) {
        console.warn('Hotkey fallback used:', data);
        this.showHotkeyValidation(data.message, 'error');

        // Update the display with the fallback hotkey
        if (data.fallbackHotkey) {
            this.config.hotkey = data.fallbackHotkey;
            this.updateHotkeyDisplay(data.fallbackHotkey);
        }

        // Show a more prominent notification
        this.showError(`Hotkey changed: ${data.message}`);
    }

    onHotkeyRegistrationFailed(data) {
        console.error('Hotkey registration failed:', data);
        this.showHotkeyValidation(data.message, 'error');
        this.showError(`Hotkey Error: ${data.message}`);

        // Offer to reset to default
        setTimeout(() => {
            if (confirm('Would you like to reset the hotkey to the default (Ctrl/Cmd + Shift + Space)?')) {
                this.resetHotkeyToDefault();
            }
        }, 2000);
    }

    async resetHotkeyToDefault() {
        try {
            const result = await ipcRenderer.invoke('reset-hotkey-to-default');
            if (result.success) {
                this.config.hotkey = 'CommandOrControl+Shift+Space';
                this.updateHotkeyDisplay(this.config.hotkey);
                this.showHotkeyValidation('Hotkey reset to default', 'success');
                this.showSuccess('Hotkey reset to default successfully');
            } else {
                this.showHotkeyValidation(result.error || 'Failed to reset hotkey', 'error');
            }
        } catch (error) {
            console.error('Failed to reset hotkey:', error);
            this.showHotkeyValidation('Failed to reset hotkey', 'error');
        }
    }

    async toggleRecording() {
        try {
            if (this.isRecording) {
                await ipcRenderer.invoke('stop-recording');
            } else {
                await ipcRenderer.invoke('start-recording');
            }
        } catch (error) {
            console.error('Failed to toggle recording:', error);
            this.showError('Failed to toggle recording');
        }
    }

    toggleSettings() {
        const configVisible = this.configSection.style.display !== 'none';
        const authVisible = this.authSection.style.display !== 'none';

        if (configVisible || authVisible) {
            // Hide both sections
            this.configSection.style.display = 'none';
            this.authSection.style.display = 'none';
            this.settingsBtn.innerHTML = '<span>⚙️ Settings</span>';
        } else {
            // Always show configuration section
            this.configSection.style.display = 'block';
            this.settingsBtn.innerHTML = '<span>⚙️ Hide Settings</span>';
            
            // Only show auth section if authenticated
            if (this.isAuthenticated) {
                this.authSection.style.display = 'block';
            } else {
                this.authSection.style.display = 'none';
            }
        }
    }

    async saveConfiguration() {
        try {
            const updatedConfig = {
                speechSettings: {
                    ...this.config.speechSettings,
                    language: this.languageSelect.value
                }
            };

            const success = await ipcRenderer.invoke('update-config', updatedConfig);

            if (success) {
                this.showSuccess('Configuration saved successfully');
                this.config = await ipcRenderer.invoke('get-config');
            } else {
                this.showError('Failed to save configuration');
            }
        } catch (error) {
            console.error('Failed to save configuration:', error);
            this.showError('Failed to save configuration');
        }
    }

    // Connection test method (replaces API key validation)
    async testConnection() {
        try {
            const isConnected = await ipcRenderer.invoke('test-connection');

            if (isConnected) {
                this.showSuccess('Backend connection successful');
            } else {
                this.showError('Backend connection failed - please check your authentication');
            }
        } catch (error) {
            console.error('Connection test failed:', error);
            this.showError('Connection test failed');
        }
    }

    onRecordingStarted() {
        this.isRecording = true;
        this.statusDot.classList.add('recording');
        this.statusText.textContent = 'Recording...';
        this.recordBtn.innerHTML = '<span>⏹️ Stop Recording</span>';
        this.recordBtn.classList.remove('btn-primary');
        this.recordBtn.classList.add('btn-primary', 'recording');
    }

    onRecordingStopped() {
        this.isRecording = false;
        this.statusDot.classList.remove('recording');
        this.statusText.textContent = 'Processing...';
        this.recordBtn.innerHTML = '<span>🎤 Start Recording</span>';
        this.recordBtn.classList.remove('recording');
        this.recordBtn.className = 'btn btn-primary';
    }

    onTranscriptionComplete(text) {
        this.statusText.textContent = 'Ready to record';
        
        // Remove placeholder class and set content
        this.transcriptionText.classList.remove('placeholder');
        this.transcriptionText.textContent = text;

        // Show a brief success indicator
        this.statusText.textContent = 'Text inserted!';
        setTimeout(() => {
            this.statusText.textContent = 'Ready to record';
        }, 2000);
    }

    onAIResponseComplete(data) {
        this.statusText.textContent = 'Ready to record';

        // Display the AI response with mode information
        const displayText = `[${data.mode.toUpperCase()} MODE]\nPrompt: "${data.prompt}"\n\nResponse:\n${data.response}`;
        
        // Remove placeholder class and set content
        this.transcriptionText.classList.remove('placeholder');
        this.transcriptionText.textContent = displayText;

        // Show success indicator with mode
        this.statusText.textContent = `${data.mode.charAt(0).toUpperCase() + data.mode.slice(1)} response inserted!`;
        setTimeout(() => {
            this.statusText.textContent = 'Ready to record';
        }, 3000);
    }

    onWindowFocus() {
        // Window gained focus
        console.log('Window focused');
    }

    onWindowBlur() {
        // Window lost focus
        console.log('Window blurred');
    }

    showError(message) {
        // Simple error display - in a real app you'd want a proper notification system
        console.error(message);
        this.statusText.textContent = `Error: ${message}`;
        this.statusText.style.color = '#dc2626'; /* red-600 for error */
        
        setTimeout(() => {
            this.statusText.textContent = 'Ready to record';
            this.statusText.style.color = '';
        }, 3000);
    }

    showSuccess(message) {
        console.log(message);
        this.statusText.textContent = message;
        this.statusText.style.color = '#16a34a'; /* green-600 for success */

        setTimeout(() => {
            this.statusText.textContent = 'Ready to record';
            this.statusText.style.color = '';
        }, 2000);
    }

    // Web Audio API recording methods
    async startWebAudioRecording(options = {}) {
        try {
            console.log('Starting Web Audio API recording with options:', options);

            // Request microphone access
            this.mediaStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: options.sampleRate || 16000,
                    channelCount: options.channelCount || 1,
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            });

            // Create audio context
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)({
                sampleRate: options.sampleRate || 16000
            });

            // Create microphone source
            this.microphone = this.audioContext.createMediaStreamSource(this.mediaStream);

            // Create script processor for real-time audio processing
            // Note: ScriptProcessorNode is deprecated but still widely supported
            // In production, you'd want to use AudioWorklet
            const bufferSize = 4096;
            this.scriptProcessor = this.audioContext.createScriptProcessor(bufferSize, 1, 1);

            // Process audio data
            this.scriptProcessor.onaudioprocess = (event) => {
                const inputBuffer = event.inputBuffer;
                const inputData = inputBuffer.getChannelData(0);

                // Convert Float32Array to regular array for IPC
                const audioData = Array.from(inputData);

                // Send audio data to main process
                ipcRenderer.send('audio-data', audioData);
            };

            // Connect the audio graph
            this.microphone.connect(this.scriptProcessor);
            this.scriptProcessor.connect(this.audioContext.destination);

            console.log('Web Audio API recording started successfully');

        } catch (error) {
            console.error('Failed to start Web Audio API recording:', error);

            // Send error to main process
            let errorMessage = 'Unknown audio error';
            if (error.name === 'NotAllowedError') {
                errorMessage = 'Microphone permission denied. Please grant microphone access and try again.';
            } else if (error.name === 'NotFoundError') {
                errorMessage = 'No microphone found. Please connect a microphone and try again.';
            } else if (error.name === 'NotReadableError') {
                errorMessage = 'Microphone is being used by another application. Please close other audio applications and try again.';
            } else if (error.name === 'AbortError') {
                errorMessage = 'Audio recording was aborted.';
            } else if (error.name === 'NotSupportedError') {
                errorMessage = 'Audio recording is not supported in this environment.';
            } else {
                errorMessage = error.message || 'Audio recording failed';
            }

            ipcRenderer.send('audio-error', errorMessage);
            this.cleanupWebAudio();
        }
    }

    stopWebAudioRecording() {
        console.log('Stopping Web Audio API recording');
        this.cleanupWebAudio();
    }

    cleanupWebAudio() {
        try {
            // Disconnect and clean up audio nodes
            if (this.scriptProcessor) {
                this.scriptProcessor.disconnect();
                this.scriptProcessor = null;
            }

            if (this.microphone) {
                this.microphone.disconnect();
                this.microphone = null;
            }

            // Stop media stream
            if (this.mediaStream) {
                this.mediaStream.getTracks().forEach(track => track.stop());
                this.mediaStream = null;
            }

            // Close audio context
            if (this.audioContext && this.audioContext.state !== 'closed') {
                this.audioContext.close();
                this.audioContext = null;
            }

            console.log('Web Audio API cleanup completed');
        } catch (error) {
            console.error('Error during Web Audio API cleanup:', error);
        }
    }

}

// Initialize the renderer app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new RendererApp();
});

// Handle uncaught errors
window.addEventListener('error', (event) => {
    console.error('Renderer error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
});
