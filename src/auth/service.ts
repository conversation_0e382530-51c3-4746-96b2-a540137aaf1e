import { createClient, SupabaseClient, User, Session } from '@supabase/supabase-js';
import { SecureAuthStorage, StoredAuthData } from './secureStorage';

export interface AuthConfig {
  supabaseUrl: string;
  supabaseAnonKey: string;
  backendUrl: string;
}

export interface AuthUser {
  id: string;
  email: string;
  created_at: string;
  updated_at: string;
}

export interface AuthSession {
  access_token: string;
  refresh_token: string;
  expires_at: number;
  user: AuthUser;
}

export interface AuthResponse {
  success: boolean;
  data?: {
    user?: AuthUser;
    session?: AuthSession;
  };
  error?: string;
}

export class AuthService {
  private supabase: SupabaseClient;
  private backendUrl: string;
  private currentSession: AuthSession | null = null;
  private authStateListeners: Array<(session: AuthSession | null) => void> = [];
  private secureStorage: SecureAuthStorage;
  private isInitialLoad = true;

  constructor(config: AuthConfig) {
    this.supabase = createClient(config.supabaseUrl, config.supabaseAnonKey);
    this.backendUrl = config.backendUrl;
    
    try {
      this.secureStorage = new SecureAuthStorage();
    } catch (error) {
      console.error('Failed to initialize secure storage:', error);
      throw new Error('Authentication service cannot be initialized without secure storage. Please ensure the application is running in a secure environment.');
    }
    
    // Listen for auth state changes
    this.supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session);
      
      if (session) {
        this.currentSession = {
          access_token: session.access_token,
          refresh_token: session.refresh_token,
          expires_at: session.expires_at || 0,
          user: {
            id: session.user.id,
            email: session.user.email || '',
            created_at: session.user.created_at,
            updated_at: session.user.updated_at || session.user.created_at,
          },
        };
        
        // Store session securely for persistence
        try {
          await this.secureStorage.storeAuthData(this.currentSession);
        } catch (error) {
          console.error('Failed to store session data:', error);
          // This is critical - if we can't store securely, we should fail
          throw new Error('Failed to store authentication data securely. Authentication cannot continue without secure storage.');
        }
      } else {
        this.currentSession = null;
        
        // Clear stored session data only if not initial load
        if (!this.isInitialLoad) {
          try {
            await this.secureStorage.clearAuthData();
          } catch (error) {
            console.error('Failed to clear stored session data:', error);
            // Continue with logout even if clearing fails
          }
        }
      }
      
      // Notify listeners
      this.authStateListeners.forEach(listener => listener(this.currentSession));

      // Set flag after initial load
      this.isInitialLoad = false;
    });
  }

  // Sign up new user
  async signUp(email: string, password: string): Promise<AuthResponse> {
    try {
      const { data, error } = await this.supabase.auth.signUp({
        email,
        password,
      });

      if (error) {
        console.error('Sign up error:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      return {
        success: true,
        data: {
          user: data.user ? {
            id: data.user.id,
            email: data.user.email || '',
            created_at: data.user.created_at,
            updated_at: data.user.updated_at || data.user.created_at,
          } : undefined,
          session: data.session ? {
            access_token: data.session.access_token,
            refresh_token: data.session.refresh_token,
            expires_at: data.session.expires_at || 0,
            user: {
              id: data.session.user.id,
              email: data.session.user.email || '',
              created_at: data.session.user.created_at,
              updated_at: data.session.user.updated_at || data.session.user.created_at,
            },
          } : undefined,
        },
      };
    } catch (error: any) {
      console.error('Sign up failed:', error);
      return {
        success: false,
        error: error.message || 'Sign up failed',
      };
    }
  }

  // Sign in user
  async signIn(email: string, password: string): Promise<AuthResponse> {
    try {
      const { data, error } = await this.supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('Sign in error:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      // Manually store the session for persistence after successful sign-in
      console.log('Starting manual session storage...');
      if (data.session) {
        console.log('Session data available from Supabase sign-in');
        const sessionData = {
          access_token: data.session.access_token,
          refresh_token: data.session.refresh_token,
          expires_at: data.session.expires_at || 0,
          user: {
            id: data.session.user.id,
            email: data.session.user.email || '',
            created_at: data.session.user.created_at,
            updated_at: data.session.user.updated_at || data.session.user.created_at,
          },
        };
        console.log('Session data prepared:', sessionData.user.email);
        try {
          console.log('Calling storeAuthData...');
          await this.secureStorage.storeAuthData(sessionData);
          console.log('Session stored successfully during sign-in');
        } catch (error) {
          console.error('Failed to store session during sign-in:', error);
          // Continue with sign-in even if storage fails, but log the error
        }
      } else {
        console.warn('No session data from Supabase sign-in');
      }

      console.log('Sign-in method completing...');

      return {
        success: true,
        data: {
          user: data.user ? {
            id: data.user.id,
            email: data.user.email || '',
            created_at: data.user.created_at,
            updated_at: data.user.updated_at || data.user.created_at,
          } : undefined,
          session: data.session ? {
            access_token: data.session.access_token,
            refresh_token: data.session.refresh_token,
            expires_at: data.session.expires_at || 0,
            user: {
              id: data.session.user.id,
              email: data.session.user.email || '',
              created_at: data.session.user.created_at,
              updated_at: data.session.user.updated_at || data.session.user.created_at,
            },
          } : undefined,
        },
      };
    } catch (error: any) {
      console.error('Sign in failed:', error);
      return {
        success: false,
        error: error.message || 'Sign in failed',
      };
    }
  }

  // Sign out user
  async signOut(): Promise<AuthResponse> {
    try {
      const { error } = await this.supabase.auth.signOut();

      if (error) {
        console.error('Sign out error:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      this.currentSession = null;
      
      // Clear stored authentication data
      try {
        await this.secureStorage.clearAuthData();
      } catch (error) {
        console.error('Failed to clear stored auth data during sign out:', error);
        // Continue with sign out even if clearing fails
      }
      
      return {
        success: true,
      };
    } catch (error: any) {
      console.error('Sign out failed:', error);
      return {
        success: false,
        error: error.message || 'Sign out failed',
      };
    }
  }

  // Get current session
  getCurrentSession(): AuthSession | null {
    return this.currentSession;
  }

  // Get current user
  getCurrentUser(): AuthUser | null {
    return this.currentSession?.user || null;
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return this.currentSession !== null;
  }

  // Get access token for API requests
  getAccessToken(): string | null {
    return this.currentSession?.access_token || null;
  }

  // Add auth state listener
  onAuthStateChange(listener: (session: AuthSession | null) => void): () => void {
    this.authStateListeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.authStateListeners.indexOf(listener);
      if (index > -1) {
        this.authStateListeners.splice(index, 1);
      }
    };
  }

  // Initialize session from stored state
  async initializeSession(): Promise<void> {
    console.log('Starting initializeSession...');

    try {
      console.log('Entering try block...');
      // First try to get session from Supabase (in-memory)
      console.log('Calling supabase.auth.getSession...');
      const getSessionResult = await this.supabase.auth.getSession();
      console.log('getSession returned:', getSessionResult);
      const { data: { session }, error } = getSessionResult;
      
      if (error) {
        console.error('getSession error:', error);
      } else {
        console.log('getSession successful, session exists:', !!session);
      }

      if (session) {
        console.log('Session found in memory:', session.user.email);
        // Session exists in memory, use it
        this.currentSession = {
          access_token: session.access_token,
          refresh_token: session.refresh_token,
          expires_at: session.expires_at || 0,
          user: {
            id: session.user.id,
            email: session.user.email || '',
            created_at: session.user.created_at,
            updated_at: session.user.updated_at || session.user.created_at,
          },
        };
        
        console.log('Session initialized from memory:', this.currentSession.user.email);
        
        // Store for persistence
        try {
          await this.secureStorage.storeAuthData(this.currentSession);
        } catch (error) {
          console.error('Failed to store session data during initialization:', error);
          // This is critical - if we can't store securely, we should fail
          throw new Error('Failed to store authentication data securely during initialization.');
        }
      } else {
        console.log('No session in memory, attempting restore from storage...');
        // No session in memory, try to restore from secure storage
        let storedAuth: StoredAuthData | null = null;
        try {
          storedAuth = await this.secureStorage.retrieveAuthData();
        } catch (error) {
          console.error('Failed to retrieve stored auth data:', error);
          // Clear potentially corrupted data
          await this.secureStorage.clearAuthData();
          throw new Error('Failed to retrieve authentication data securely. Please sign in again.');
        }
        
        if (storedAuth) {
          // Check if token is expired
          if (this.secureStorage.isTokenExpired(storedAuth)) {
            console.log('Stored token is expired, attempting refresh...');
            
            // Try to refresh the token
            const refreshResult = await this.refreshToken(storedAuth.refresh_token);
            
            if (refreshResult.success && refreshResult.data?.session) {
              this.currentSession = refreshResult.data.session;
              console.log('Session refreshed successfully:', this.currentSession?.user.email);
            } else {
              console.log('Token refresh failed, clearing stored data');
              await this.secureStorage.clearAuthData();
            }
          } else {
            // Token is still valid, restore session
            this.currentSession = storedAuth;
            console.log('Session restored from secure storage:', this.currentSession.user.email);
            
            // Set the session in Supabase client
            await this.supabase.auth.setSession({
              access_token: storedAuth.access_token,
              refresh_token: storedAuth.refresh_token,
            });
          }
        }
      }
      
      // Notify listeners about the initialized session
      this.authStateListeners.forEach(listener => listener(this.currentSession));
      console.log('Try block completed successfully');
    } catch (error: unknown) {
      console.log('Entered catch block');
      console.error('Failed to initialize session:', error);
      let message = 'Unknown error';
      let stack = '';
      if (error instanceof Error) {
        message = error.message;
        stack = error.stack || '';
      }
      console.error('Error details:', message, stack);
      // Clear potentially corrupted data
      console.warn('Clearing potentially corrupted data due to initialization error');
      await this.secureStorage.clearAuthData();
      console.log('Catch block completed');
    }

    console.log('initializeSession completed');
  }

  // Reset password
  async resetPassword(email: string): Promise<AuthResponse> {
    try {
      const { error } = await this.supabase.auth.resetPasswordForEmail(email);

      if (error) {
        console.error('Reset password error:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      return {
        success: true,
      };
    } catch (error: any) {
      console.error('Reset password failed:', error);
      return {
        success: false,
        error: error.message || 'Reset password failed',
      };
    }
  }



  // Refresh access token using refresh token
  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    try {
      const { data, error } = await this.supabase.auth.refreshSession({
        refresh_token: refreshToken,
      });

      if (error) {
        console.error('Token refresh error:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      if (data.session) {
        const session: AuthSession = {
          access_token: data.session.access_token,
          refresh_token: data.session.refresh_token,
          expires_at: data.session.expires_at || 0,
          user: {
            id: data.session.user.id,
            email: data.session.user.email || '',
            created_at: data.session.user.created_at,
            updated_at: data.session.user.updated_at || data.session.user.created_at,
          },
        };

        return {
          success: true,
          data: {
            session,
            user: session.user,
          },
        };
      }

      return {
        success: false,
        error: 'No session returned from refresh',
      };
    } catch (error: any) {
      console.error('Token refresh failed:', error);
      return {
        success: false,
        error: error.message || 'Token refresh failed',
      };
    }
  }
}
