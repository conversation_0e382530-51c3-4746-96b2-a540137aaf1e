import { safeStorage } from 'electron';
import * as fs from 'fs';
import * as path from 'path';
import { app } from 'electron';

export interface StoredAuthData {
  access_token: string;
  refresh_token: string;
  expires_at: number;
  user: {
    id: string;
    email: string;
    created_at: string;
    updated_at: string;
  };
}

export class SecureAuthStorage {
  private storagePath: string;
  private encryptionAvailable: boolean;

  constructor() {
    this.storagePath = this.getStoragePath();
    this.encryptionAvailable = safeStorage.isEncryptionAvailable();
    
    if (!this.encryptionAvailable) {
      throw new Error('Encryption is not available. Secure storage cannot be initialized without encryption.');
    }
  }

  private getStoragePath(): string {
    const userDataPath = app.getPath('userData');
    return path.join(userDataPath, 'auth.dat');
  }

  /**
   * Store authentication data securely
   */
  async storeAuthData(authData: StoredAuthData): Promise<boolean> {
    try {
      if (!this.encryptionAvailable) {
        throw new Error('Encryption is not available. Cannot store auth data without encryption.');
      }

      const jsonData = JSON.stringify(authData);
      
      // Use Electron's secure storage with encryption (always encrypted)
      const encryptedData = safeStorage.encryptString(jsonData);
      
      // Ensure directory exists
      const storageDir = path.dirname(this.storagePath);
      if (!fs.existsSync(storageDir)) {
        fs.mkdirSync(storageDir, { recursive: true });
      }
      
      // Write encrypted data to file
      fs.writeFileSync(this.storagePath, encryptedData);
      
      console.log('Auth data stored securely with encryption');
      return true;
    } catch (error) {
      console.error('Failed to store auth data:', error);
      throw error; // Re-throw to let caller handle the error
    }
  }

  /**
   * Retrieve authentication data securely
   */
  async retrieveAuthData(): Promise<StoredAuthData | null> {
    try {
      if (!this.encryptionAvailable) {
        throw new Error('Encryption is not available. Cannot retrieve auth data without encryption.');
      }

      if (!fs.existsSync(this.storagePath)) {
        return null;
      }
      
      const encryptedData = fs.readFileSync(this.storagePath);
      
      // Decrypt using Electron's secure storage (always encrypted)
      const jsonData = safeStorage.decryptString(encryptedData);
      
      const authData: StoredAuthData = JSON.parse(jsonData);
      
      // Validate the data structure
      if (!this.validateAuthData(authData)) {
        console.warn('Invalid auth data structure, clearing storage');
        await this.clearAuthData();
        return null;
      }
      
      console.log('Auth data retrieved successfully with encryption');
      return authData;
    } catch (error) {
      console.error('Failed to retrieve auth data:', error);
      // Clear corrupted data
      await this.clearAuthData();
      throw error; // Re-throw to let caller handle the error
    }
  }

  /**
   * Clear stored authentication data
   */
  async clearAuthData(): Promise<boolean> {
    try {
      if (fs.existsSync(this.storagePath)) {
        fs.unlinkSync(this.storagePath);
        console.log('Auth data cleared');
      }
      return true;
    } catch (error) {
      console.error('Failed to clear auth data:', error);
      return false;
    }
  }

  /**
   * Check if auth data exists
   */
  hasStoredAuthData(): boolean {
    return fs.existsSync(this.storagePath);
  }

  /**
   * Validate auth data structure
   */
  private validateAuthData(data: any): data is StoredAuthData {
    return (
      data &&
      typeof data === 'object' &&
      typeof data.access_token === 'string' &&
      typeof data.refresh_token === 'string' &&
      typeof data.expires_at === 'number' &&
      data.user &&
      typeof data.user.id === 'string' &&
      typeof data.user.email === 'string' &&
      typeof data.user.created_at === 'string' &&
      typeof data.user.updated_at === 'string'
    );
  }

  /**
   * Check if stored token is expired
   */
  isTokenExpired(authData: StoredAuthData): boolean {
    const now = Math.floor(Date.now() / 1000);
    // Add 5 minute buffer to account for clock differences
    return authData.expires_at <= (now + 300);
  }

  /**
   * Get encryption status
   */
  isEncryptionAvailable(): boolean {
    return this.encryptionAvailable;
  }

  /**
   * Get storage file path (for debugging)
   */
  getStorageFilePath(): string {
    return this.storagePath;
  }
} 