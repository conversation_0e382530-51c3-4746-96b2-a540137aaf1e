import { clipboard } from 'electron';
import { ApiService, TranscriptionOptions as ApiTranscriptionOptions, AIProcessingOptions as ApiAIProcessingOptions } from '../api/service';

// Re-export types from API service for backward compatibility
export type TranscriptionOptions = ApiTranscriptionOptions;
export type AIProcessingOptions = ApiAIProcessingOptions;

export class SpeechRecognizer {
  private apiService: ApiService | null = null;

  constructor() {
    // API service will be set by the main process after authentication
  }

  setApiService(apiService: ApiService): void {
    this.apiService = apiService;
  }

  // Check if API service is available (user is authenticated)
  isReady(): boolean {
    return this.apiService !== null;
  }

  async testConnection(): Promise<boolean> {
    if (!this.apiService) {
      console.log('No API service available - user not authenticated');
      return false;
    }

    try {
      return await this.apiService.testConnection();
    } catch (error) {
      console.error('Connection test failed:', error);
      return false;
    }
  }

  async transcribe(audioData: <PERSON>uff<PERSON>, options: TranscriptionOptions = {}): Promise<string | null> {
    // Check if user is authenticated and API service is available
    if (!this.apiService) {
      console.log('No API service available - user not authenticated, returning mock transcription');

      // Analyze the audio data to provide more realistic mock behavior
      const audioAnalysis = this.analyzeAudioForMockTranscription(audioData);

      if (audioAnalysis.isEmpty || audioAnalysis.averageLevel < 0.01) {
        console.log('Mock analysis: Audio appears to be silent or empty');
        return null; // Return null for silent audio
      }

      if (audioAnalysis.averageLevel < 0.05) {
        console.log('Mock analysis: Audio level too low for reliable transcription');
        return null; // Return null for very quiet audio
      }

      // Return different mock responses based on audio characteristics
      const mockResponses = [
        'Hello, this is a test transcription.',
        'Testing the voice recognition system.',
        'This would be your actual speech transcribed.',
        'Mock transcription of your voice input.',
        'Voice recognition is working correctly.'
      ];

      // Use audio characteristics to select a consistent mock response
      const responseIndex = Math.floor(audioAnalysis.averageLevel * 100) % mockResponses.length;
      const mockResponse = mockResponses[responseIndex];

      console.log(`Mock transcription generated: "${mockResponse}"`);
      return mockResponse;
    }

    try {
      console.log(`Transcribing ${audioData.length} bytes of audio data via backend...`);

      // Validate that we have proper WAV format
      if (!this.isValidWAVBuffer(audioData)) {
        throw new Error('Invalid audio format: Expected WAV format with proper header');
      }

      // Use the API service to transcribe
      return await this.apiService.transcribe(audioData, options);
    } catch (error: any) {
      console.error('Transcription failed:', error);
      throw error; // Re-throw the error from API service
    }
  }

  async processWithAI(text: string, options: AIProcessingOptions): Promise<string | null> {
    if (!this.apiService) {
      console.log('No API service available - user not authenticated, returning mock AI response');
      return `[DEMO MODE] Mock ${options.mode} response for: "${options.prompt}". This would be generated by GPT-4 when you sign in.`;
    }

    try {
      console.log('Processing AI request via backend:', { mode: options.mode, prompt: options.prompt });
      return await this.apiService.processWithAI(text, options);
    } catch (error: any) {
      console.error('AI processing failed:', error);
      throw error; // Re-throw the error from API service
    }
  }

  // Get usage statistics
  async getUsageStats(days = 30): Promise<any> {
    if (!this.apiService) {
      console.log('No API service available - user not authenticated');
      return {
        totalRequests: 0,
        totalTokens: 0,
        totalAudioDuration: 0,
        requestsByEndpoint: {},
      };
    }

    try {
      return await this.apiService.getUsageStats(days);
    } catch (error) {
      console.error('Failed to get usage stats:', error);
      return {
        totalRequests: 0,
        totalTokens: 0,
        totalAudioDuration: 0,
        requestsByEndpoint: {},
      };
    }
  }

  /**
   * Validate that the buffer contains a proper WAV file
   */
  private isValidWAVBuffer(buffer: Buffer): boolean {
    if (buffer.length < 44) {
      console.warn('Audio buffer too small for WAV format (< 44 bytes)');
      return false;
    }

    // Check for RIFF header
    const riffHeader = buffer.slice(0, 4).toString('ascii');
    if (riffHeader !== 'RIFF') {
      console.warn(`Invalid WAV header: Expected 'RIFF', got '${riffHeader}'`);
      return false;
    }

    // Check for WAVE format
    const waveFormat = buffer.slice(8, 12).toString('ascii');
    if (waveFormat !== 'WAVE') {
      console.warn(`Invalid WAV format: Expected 'WAVE', got '${waveFormat}'`);
      return false;
    }

    console.log('✓ Valid WAV format detected');
    return true;
  }

  // Helper method to safely read clipboard
  private safeReadClipboard(): string {
    try {
      return clipboard?.readText() || '';
    } catch (error: any) {
      console.warn('Clipboard not available:', error?.message || 'Unknown error');
      return '';
    }
  }

  // Method to detect if the transcribed text is an AI command
  detectAICommand(text: string): AIProcessingOptions | null {
    const lowerText = text.toLowerCase().trim();

    // Write mode - supports clipboard references
    if (lowerText.startsWith('write ')) {
      const prompt = text.substring(6).trim();
      return {
        mode: 'write',
        prompt: prompt,
        clipboardContent: prompt.toLowerCase().includes('clipboard') ? this.safeReadClipboard() : undefined
      };
    }

    // Answer mode - direct questions to GPT-4
    if (lowerText.startsWith('answer ')) {
      return {
        mode: 'answer',
        prompt: text.substring(7).trim()
      };
    }

    // Alternative answer patterns
    if (lowerText.startsWith('answer the question:') || lowerText.startsWith('answer:')) {
      const colonIndex = text.indexOf(':');
      return {
        mode: 'answer',
        prompt: text.substring(colonIndex + 1).trim()
      };
    }

    // Rewrite mode - works with selected text or clipboard
    if (lowerText.startsWith('rewrite ')) {
      return {
        mode: 'rewrite',
        prompt: text.substring(8).trim(),
        clipboardContent: this.safeReadClipboard()
      };
    }

    // Reply mode - uses clipboard for email/message context
    if (lowerText.startsWith('reply ')) {
      return {
        mode: 'reply',
        prompt: text.substring(6).trim(),
        clipboardContent: this.safeReadClipboard()
      };
    }

    // Command mode
    if (lowerText.startsWith('run command ') || lowerText.startsWith('open ')) {
      return {
        mode: 'command',
        prompt: text.trim()
      };
    }

    return null;
  }

  // Get supported languages for Whisper
  getSupportedLanguages(): string[] {
    return [
      'en', 'zh', 'de', 'es', 'ru', 'ko', 'fr', 'ja', 'pt', 'tr', 'pl', 'ca', 'nl', 
      'ar', 'sv', 'it', 'id', 'hi', 'fi', 'vi', 'he', 'uk', 'el', 'ms', 'cs', 'ro', 
      'da', 'hu', 'ta', 'no', 'th', 'ur', 'hr', 'bg', 'lt', 'la', 'mi', 'ml', 'cy', 
      'sk', 'te', 'fa', 'lv', 'bn', 'sr', 'az', 'sl', 'kn', 'et', 'mk', 'br', 'eu', 
      'is', 'hy', 'ne', 'mn', 'bs', 'kk', 'sq', 'sw', 'gl', 'mr', 'pa', 'si', 'km', 
      'sn', 'yo', 'so', 'af', 'oc', 'ka', 'be', 'tg', 'sd', 'gu', 'am', 'yi', 'lo', 
      'uz', 'fo', 'ht', 'ps', 'tk', 'nn', 'mt', 'sa', 'lb', 'my', 'bo', 'tl', 'mg', 
      'as', 'tt', 'haw', 'ln', 'ha', 'ba', 'jw', 'su'
    ];
  }

  /**
   * Analyze audio buffer for mock transcription behavior
   * This mimics basic audio analysis to provide more realistic mock responses
   */
  private analyzeAudioForMockTranscription(audioBuffer: Buffer): {
    isEmpty: boolean;
    averageLevel: number;
    maxLevel: number;
    dataSize: number;
  } {
    if (!audioBuffer || audioBuffer.length === 0) {
      return { isEmpty: true, averageLevel: 0, maxLevel: 0, dataSize: 0 };
    }

    // Skip WAV header (44 bytes) and analyze PCM data
    const dataStart = 44;
    if (audioBuffer.length <= dataStart) {
      return { isEmpty: true, averageLevel: 0, maxLevel: 0, dataSize: 0 };
    }

    const pcmData = audioBuffer.slice(dataStart);
    const samples = [];
    
    // Read 16-bit PCM samples
    for (let i = 0; i < pcmData.length - 1; i += 2) {
      const sample = pcmData.readInt16LE(i);
      samples.push(Math.abs(sample) / 32768); // Normalize to 0-1
    }

    if (samples.length === 0) {
      return { isEmpty: true, averageLevel: 0, maxLevel: 0, dataSize: pcmData.length };
    }

    const averageLevel = samples.reduce((sum, sample) => sum + sample, 0) / samples.length;
    const maxLevel = Math.max(...samples);
    const isEmpty = averageLevel < 0.001 && maxLevel < 0.01;

    return {
      isEmpty,
      averageLevel,
      maxLevel,
      dataSize: pcmData.length
    };
  }
}
