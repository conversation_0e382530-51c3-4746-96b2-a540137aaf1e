import axios, { AxiosResponse } from 'axios';
import FormData from 'form-data';
import { AuthService } from '../auth/service';

export interface TranscriptionOptions {
  language?: string;
  prompt?: string;
  temperature?: number;
  response_format?: 'json' | 'text' | 'srt' | 'verbose_json' | 'vtt';
}

export interface AIProcessingOptions {
  mode: 'write' | 'answer' | 'rewrite' | 'reply' | 'command';
  prompt: string;
  context?: string;
  selectedText?: string;
  clipboardContent?: string;
}

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface ChatCompletionRequest {
  model: string;
  messages: ChatMessage[];
  max_tokens?: number;
  temperature?: number;
}

export interface UsageStats {
  totalRequests: number;
  totalTokens: number;
  totalAudioDuration: number;
  requestsByEndpoint: Record<string, number>;
}

export class ApiService {
  private backendUrl: string;
  private authService: AuthService;

  constructor(backendUrl: string, authService: AuthService) {
    this.backendUrl = backendUrl;
    this.authService = authService;
  }

  // Get authorization headers
  private getAuthHeaders(): Record<string, string> {
    const token = this.authService.getAccessToken();
    if (!token) {
      throw new Error('Not authenticated');
    }
    
    return {
      'Authorization': `Bearer ${token}`,
    };
  }

  /**
   * Calculate adaptive timeout based on audio size
   * Shorter recordings should have shorter timeouts for better responsiveness
   */
  private calculateTimeout(audioSize: number): number {
    // Base timeout of 5 seconds + 1 second per 100KB
    // This gives ~5s for very short recordings, ~15s for 1MB files
    const baseTimeout = 5000;
    const sizeBasedTimeout = Math.ceil(audioSize / 102400) * 1000; // 1s per 100KB
    const maxTimeout = 30000; // Cap at 30 seconds
    
    return Math.min(baseTimeout + sizeBasedTimeout, maxTimeout);
  }

  /**
   * Estimate audio duration from buffer size for better timeout calculation
   */
  private estimateAudioDuration(bufferSize: number): number {
    // Rough estimation: 16kHz * 2 bytes * 1 channel ≈ 32KB per second
    const bytesPerSecond = 32000;
    const headerSize = 44; // WAV header
    const audioDataSize = Math.max(0, bufferSize - headerSize);
    return audioDataSize / bytesPerSecond;
  }

  // Test backend connectivity
  async testConnection(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.backendUrl}/api/openai/test`, {
        headers: this.getAuthHeaders(),
        timeout: 5000, // Keep short timeout for health checks
      });
      
      return response.data.success && response.data.data.apiKeyValid;
    } catch (error) {
      console.error('Backend connection test failed:', error);
      return false;
    }
  }

  // Transcribe audio using backend proxy
  async transcribe(audioData: Buffer, options: TranscriptionOptions = {}): Promise<string | null> {
    const startTime = Date.now();
    
    try {
      console.log(`Transcribing ${audioData.length} bytes of audio data via backend...`);

      // Performance optimization: Calculate adaptive timeout
      const timeout = this.calculateTimeout(audioData.length);
      const estimatedDuration = this.estimateAudioDuration(audioData.length);
      
      console.log(`Audio estimated duration: ${estimatedDuration.toFixed(1)}s, using timeout: ${timeout}ms`);

      // Create form data for the API request
      const formData = new FormData();
      
      // Add the audio file with optimized filename
      const filename = `speech_${Date.now()}.wav`;
      formData.append('file', audioData, {
        filename,
        contentType: 'audio/wav',
      });
      
      // Add optional parameters
      if (options.language) {
        formData.append('language', options.language);
      }
      
      if (options.prompt) {
        formData.append('prompt', options.prompt);
      }
      
      if (options.temperature !== undefined) {
        formData.append('temperature', options.temperature.toString());
      }
      
      // Default to 'text' format for better performance (less parsing)
      const responseFormat = options.response_format || 'text';
      formData.append('response_format', responseFormat);

      const response: AxiosResponse = await axios.post(
        `${this.backendUrl}/api/openai/transcribe`,
        formData,
        {
          headers: {
            ...this.getAuthHeaders(),
            ...formData.getHeaders(),
          },
          timeout, // Use adaptive timeout
        }
      );

      const processingTime = Date.now() - startTime;
      console.log(`Transcription completed in ${processingTime}ms`);

      if (response.data) {
        // Handle different response formats
        if (responseFormat === 'json' || responseFormat === 'verbose_json') {
          return (response.data.text || '').trim();
        } else {
          return (response.data || '').trim();
        }
      }

      return null;
    } catch (error: any) {
      const processingTime = Date.now() - startTime;
      console.error(`Transcription failed after ${processingTime}ms:`, error);

      if (error.response) {
        console.error('API Error:', error.response.data);
        
        // Provide more specific error messages based on status code
        if (error.response.status === 413) {
          throw new Error('Audio file too large for transcription (max 25MB)');
        } else if (error.response.status === 408 || error.code === 'ECONNABORTED') {
          throw new Error('Transcription timed out - try a shorter recording');
        } else {
          throw new Error(`Transcription failed: ${error.response.data.error || 'Unknown API error'}`);
        }
      } else if (error.request) {
        throw new Error('Network error: Unable to reach backend service');
      } else {
        throw new Error(`Transcription failed: ${error.message || String(error)}`);
      }
    }
  }

  // Process text with AI using backend proxy
  async processWithAI(text: string, options: AIProcessingOptions): Promise<string | null> {
    const startTime = Date.now();
    
    try {
      console.log('Processing AI request via backend:', { mode: options.mode, prompt: options.prompt });
      
      // Calculate timeout based on text length and processing complexity
      const baseTimeout = 10000; // 10 seconds base
      const textLengthTimeout = Math.min(text.length * 10, 15000); // Max 15s for text length
      const timeout = baseTimeout + textLengthTimeout;
      
      const response = await axios.post(
        `${this.backendUrl}/api/openai/process-ai`,
        {
          text,
          options,
        },
        {
          headers: {
            ...this.getAuthHeaders(),
            'Content-Type': 'application/json',
          },
          timeout,
        }
      );

      const processingTime = Date.now() - startTime;
      console.log(`AI processing completed in ${processingTime}ms`);

      if (response.data.success) {
        return response.data.data.response;
      } else {
        throw new Error(response.data.error || 'AI processing failed');
      }
    } catch (error: any) {
      const processingTime = Date.now() - startTime;
      console.error(`AI processing failed after ${processingTime}ms:`, error);
      
      if (error.response) {
        console.error('API Error:', error.response.data);
        
        if (error.response.status === 408 || error.code === 'ECONNABORTED') {
          throw new Error('AI processing timed out - try a shorter or simpler request');
        } else {
          throw new Error(`AI processing failed: ${error.response.data.error || 'Unknown API error'}`);
        }
      } else if (error.request) {
        throw new Error('Network error: Unable to reach backend service');
      } else {
        throw new Error(`AI processing failed: ${error.message || String(error)}`);
      }
    }
  }

  // Chat completion using backend proxy
  async chatCompletion(request: ChatCompletionRequest): Promise<any> {
    try {
      console.log('Making chat completion request via backend');
      
      const response = await axios.post(
        `${this.backendUrl}/api/openai/chat/completions`,
        request,
        {
          headers: {
            ...this.getAuthHeaders(),
            'Content-Type': 'application/json',
          },
          timeout: 30000,
        }
      );

      return response.data;
    } catch (error: any) {
      console.error('Chat completion failed:', error);
      
      if (error.response) {
        console.error('API Error:', error.response.data);
        throw new Error(`Chat completion failed: ${error.response.data.error || 'Unknown API error'}`);
      } else if (error.request) {
        throw new Error('Network error: Unable to reach backend service');
      } else {
        throw new Error(`Chat completion failed: ${error.message || String(error)}`);
      }
    }
  }

  // Get user usage statistics
  async getUsageStats(days = 30): Promise<UsageStats> {
    try {
      const response = await axios.get(
        `${this.backendUrl}/api/openai/usage?days=${days}`,
        {
          headers: this.getAuthHeaders(),
          timeout: 10000,
        }
      );

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.error || 'Failed to get usage stats');
      }
    } catch (error: any) {
      console.error('Failed to get usage stats:', error);
      
      // Return empty stats on error
      return {
        totalRequests: 0,
        totalTokens: 0,
        totalAudioDuration: 0,
        requestsByEndpoint: {},
      };
    }
  }

  // Health check
  async healthCheck(): Promise<{ healthy: boolean; services: Record<string, boolean> }> {
    try {
      const response = await axios.get(`${this.backendUrl}/api/health/detailed`, {
        timeout: 5000,
      });

      return {
        healthy: response.data.success,
        services: response.data.services || {},
      };
    } catch (error) {
      console.error('Health check failed:', error);
      return {
        healthy: false,
        services: {},
      };
    }
  }
}
