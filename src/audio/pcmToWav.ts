/**
 * Converts PCM 16-bit little-endian audio data to WAV format
 * @param buffer - PCM audio data buffer
 * @param sampleRate - Sample rate in Hz (default: 16000)
 * @param channels - Number of audio channels (default: 1)
 * @returns WAV formatted buffer
 */
export function pcm16leToWav(buffer: Buffer, sampleRate = 16000, channels = 1): Buffer {
  const byteRate = sampleRate * channels * 2;
  const blockAlign = channels * 2;
  const bitsPerSample = 16;
  const dataSize = buffer.length;
  const fileSize = 44 + dataSize;

  // Create 44-byte RIFF/WAVE header
  const header = Buffer.alloc(44);
  let offset = 0;

  // RIFF header
  header.write('RIFF', offset); offset += 4;
  header.writeUInt32LE(fileSize - 8, offset); offset += 4;
  header.write('WAVE', offset); offset += 4;

  // fmt chunk
  header.write('fmt ', offset); offset += 4;
  header.writeUInt32LE(16, offset); offset += 4; // PCM chunk size
  header.writeUInt16LE(1, offset); offset += 2;  // PCM format
  header.writeUInt16LE(channels, offset); offset += 2;
  header.writeUInt32LE(sampleRate, offset); offset += 4;
  header.writeUInt32LE(byteRate, offset); offset += 4;
  header.writeUInt16LE(blockAlign, offset); offset += 2;
  header.writeUInt16LE(bitsPerSample, offset); offset += 2;

  // data chunk
  header.write('data', offset); offset += 4;
  header.writeUInt32LE(dataSize, offset); offset += 4;

  return Buffer.concat([header, buffer]);
}
