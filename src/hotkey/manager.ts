import { globalShortcut } from 'electron';
import { UiohookKey, UiohookKeyboardEvent, uIOhook } from 'uiohook-napi';

export interface HotkeyConfig {
  accelerator: string;
  callback: () => void;
  description?: string;
}

export interface PressHoldHotkeyConfig {
  accelerator: string;
  onKeyDown: () => void;
  onKeyUp: () => void;
  description?: string;
}

export interface SupportedKey {
  key: string;
  displayName: string;
  category: 'modifier' | 'function' | 'alphanumeric' | 'special';
}

export class HotkeyManager {
  private registeredHotkeys: Map<string, HotkeyConfig> = new Map();
  private currentHotkey: string | null = null;
  private pressHoldHotkeys: Map<string, PressHoldHotkeyConfig> = new Map();
  private currentPressHoldHotkey: string | null = null;
  private isUiohookStarted: boolean = false;
  private pressedKeys: Set<number> = new Set();
  private currentModifiers: Set<string> = new Set();
  private activeHotkeys: Set<string> = new Set(); // Track which hotkeys are currently active

  // Supported modifier keys
  private static readonly MODIFIERS: SupportedKey[] = [
    { key: 'CommandOrControl', displayName: 'Ctrl/Cmd', category: 'modifier' },
    { key: 'Shift', displayName: 'Shift', category: 'modifier' },
    { key: 'Alt', displayName: 'Alt', category: 'modifier' },
    { key: 'Super', displayName: 'Super', category: 'modifier' }, // Windows key on Windows, Cmd on macOS
  ];

  // Supported function keys
  private static readonly FUNCTION_KEYS: SupportedKey[] = [
    { key: 'F1', displayName: 'F1', category: 'function' },
    { key: 'F2', displayName: 'F2', category: 'function' },
    { key: 'F3', displayName: 'F3', category: 'function' },
    { key: 'F4', displayName: 'F4', category: 'function' },
    { key: 'F5', displayName: 'F5', category: 'function' },
    { key: 'F6', displayName: 'F6', category: 'function' },
    { key: 'F7', displayName: 'F7', category: 'function' },
    { key: 'F8', displayName: 'F8', category: 'function' },
    { key: 'F9', displayName: 'F9', category: 'function' },
    { key: 'F10', displayName: 'F10', category: 'function' },
    { key: 'F11', displayName: 'F11', category: 'function' },
    { key: 'F12', displayName: 'F12', category: 'function' },
  ];

  // Supported alphanumeric keys
  private static readonly ALPHANUMERIC_KEYS: SupportedKey[] = [
    ...Array.from({ length: 26 }, (_, i) => ({
      key: String.fromCharCode(65 + i),
      displayName: String.fromCharCode(65 + i),
      category: 'alphanumeric' as const,
    })),
    ...Array.from({ length: 10 }, (_, i) => ({
      key: i.toString(),
      displayName: i.toString(),
      category: 'alphanumeric' as const,
    })),
  ];

  // Supported special keys
  private static readonly SPECIAL_KEYS: SupportedKey[] = [
    { key: 'Space', displayName: 'Space', category: 'special' },
    { key: 'Tab', displayName: 'Tab', category: 'special' },
    { key: 'Enter', displayName: 'Enter', category: 'special' },
    { key: 'Escape', displayName: 'Esc', category: 'special' },
    { key: 'Backspace', displayName: 'Backspace', category: 'special' },
    { key: 'Delete', displayName: 'Delete', category: 'special' },
    { key: 'Insert', displayName: 'Insert', category: 'special' },
    { key: 'Home', displayName: 'Home', category: 'special' },
    { key: 'End', displayName: 'End', category: 'special' },
    { key: 'PageUp', displayName: 'Page Up', category: 'special' },
    { key: 'PageDown', displayName: 'Page Down', category: 'special' },
    { key: 'Up', displayName: '↑', category: 'special' },
    { key: 'Down', displayName: '↓', category: 'special' },
    { key: 'Left', displayName: '←', category: 'special' },
    { key: 'Right', displayName: '→', category: 'special' },
  ];

  // System-reserved hotkeys that should not be allowed
  private static readonly RESERVED_HOTKEYS = [
    'CommandOrControl+C',
    'CommandOrControl+V',
    'CommandOrControl+X',
    'CommandOrControl+Z',
    'CommandOrControl+Y',
    'CommandOrControl+A',
    'CommandOrControl+S',
    'CommandOrControl+O',
    'CommandOrControl+N',
    'CommandOrControl+W',
    'CommandOrControl+Q',
    'CommandOrControl+R',
    'CommandOrControl+T',
    'CommandOrControl+F',
    'CommandOrControl+H',
    'Alt+F4',
    'CommandOrControl+Alt+Delete',
    'CommandOrControl+Shift+Escape',
  ];

  // Key mapping from Electron accelerator format to uiohook key codes
  private static readonly KEY_MAP: Record<string, number> = {
    // Modifier keys
    'Control': UiohookKey.Ctrl,
    'CommandOrControl': process.platform === 'darwin' ? UiohookKey.Meta : UiohookKey.Ctrl,
    'Shift': UiohookKey.Shift,
    'Alt': UiohookKey.Alt,
    'Super': UiohookKey.Meta,

    // Function keys
    'F1': UiohookKey.F1, 'F2': UiohookKey.F2, 'F3': UiohookKey.F3, 'F4': UiohookKey.F4,
    'F5': UiohookKey.F5, 'F6': UiohookKey.F6, 'F7': UiohookKey.F7, 'F8': UiohookKey.F8,
    'F9': UiohookKey.F9, 'F10': UiohookKey.F10, 'F11': UiohookKey.F11, 'F12': UiohookKey.F12,

    // Special keys
    'Space': UiohookKey.Space,
    'Tab': UiohookKey.Tab,
    'Enter': UiohookKey.Enter,
    'Escape': UiohookKey.Escape,
    'Backspace': UiohookKey.Backspace,
    'Delete': UiohookKey.Delete,
    'Insert': UiohookKey.Insert,
    'Home': UiohookKey.Home,
    'End': UiohookKey.End,
    'PageUp': UiohookKey.PageUp,
    'PageDown': UiohookKey.PageDown,
    'Up': UiohookKey.ArrowUp,
    'Down': UiohookKey.ArrowDown,
    'Left': UiohookKey.ArrowLeft,
    'Right': UiohookKey.ArrowRight,

    // Alphanumeric keys
    'A': UiohookKey.A, 'B': UiohookKey.B, 'C': UiohookKey.C, 'D': UiohookKey.D,
    'E': UiohookKey.E, 'F': UiohookKey.F, 'G': UiohookKey.G, 'H': UiohookKey.H,
    'I': UiohookKey.I, 'J': UiohookKey.J, 'K': UiohookKey.K, 'L': UiohookKey.L,
    'M': UiohookKey.M, 'N': UiohookKey.N, 'O': UiohookKey.O, 'P': UiohookKey.P,
    'Q': UiohookKey.Q, 'R': UiohookKey.R, 'S': UiohookKey.S, 'T': UiohookKey.T,
    'U': UiohookKey.U, 'V': UiohookKey.V, 'W': UiohookKey.W, 'X': UiohookKey.X,
    'Y': UiohookKey.Y, 'Z': UiohookKey.Z,

    // Number keys (using the string keys directly from UiohookKey)
    '0': UiohookKey['0'], '1': UiohookKey['1'], '2': UiohookKey['2'], '3': UiohookKey['3'],
    '4': UiohookKey['4'], '5': UiohookKey['5'], '6': UiohookKey['6'], '7': UiohookKey['7'],
    '8': UiohookKey['8'], '9': UiohookKey['9'],
  };

  /**
   * Get all supported keys organized by category
   */
  static getSupportedKeys(): Record<string, SupportedKey[]> {
    return {
      modifiers: HotkeyManager.MODIFIERS,
      function: HotkeyManager.FUNCTION_KEYS,
      alphanumeric: HotkeyManager.ALPHANUMERIC_KEYS,
      special: HotkeyManager.SPECIAL_KEYS,
    };
  }

  /**
   * Validate a hotkey accelerator string
   */
  static validateAccelerator(accelerator: string): { valid: boolean; error?: string } {
    if (!accelerator || typeof accelerator !== 'string') {
      return { valid: false, error: 'Accelerator must be a non-empty string' };
    }

    // Check if it's a reserved hotkey
    if (HotkeyManager.RESERVED_HOTKEYS.includes(accelerator)) {
      return { valid: false, error: 'This hotkey is reserved by the system' };
    }

    const parts = accelerator.split('+');

    if (parts.length < 2) {
      return { valid: false, error: 'Hotkey must include at least one modifier key' };
    }

    // Separate modifiers from main key
    const validModifiers = HotkeyManager.MODIFIERS.map(m => m.key);
    const allKeys = [
      ...HotkeyManager.FUNCTION_KEYS,
      ...HotkeyManager.ALPHANUMERIC_KEYS,
      ...HotkeyManager.SPECIAL_KEYS,
    ];
    const validKeys = allKeys.map(k => k.key);

    let modifiers: string[] = [];
    let mainKey: string | null = null;

    // Parse parts to separate modifiers from main key
    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];
      if (validModifiers.includes(part)) {
        modifiers.push(part);
      } else if (validKeys.includes(part)) {
        if (mainKey === null) {
          mainKey = part;
        } else {
          return { valid: false, error: 'Multiple non-modifier keys found' };
        }
      } else {
        return { valid: false, error: `Invalid key: ${part}` };
      }
    }

    if (modifiers.length === 0) {
      return { valid: false, error: 'Hotkey must include at least one modifier key' };
    }

    if (mainKey === null) {
      return { valid: false, error: 'Hotkey must include a main key' };
    }

    return { valid: true };
  }

  /**
   * Format accelerator for display
   */
  static formatAcceleratorForDisplay(accelerator: string): string {
    if (!accelerator) return '';

    const parts = accelerator.split('+');
    const displayParts: string[] = [];

    for (const part of parts) {
      // Find the display name for this part
      const allKeys = [
        ...HotkeyManager.MODIFIERS,
        ...HotkeyManager.FUNCTION_KEYS,
        ...HotkeyManager.ALPHANUMERIC_KEYS,
        ...HotkeyManager.SPECIAL_KEYS,
      ];
      
      const keyInfo = allKeys.find(k => k.key === part);
      displayParts.push(keyInfo ? keyInfo.displayName : part);
    }

    return displayParts.join(' + ');
  }

  /**
   * Register a global hotkey
   */
  async registerHotkey(config: HotkeyConfig): Promise<{ success: boolean; error?: string }> {
    const validation = HotkeyManager.validateAccelerator(config.accelerator);
    if (!validation.valid) {
      return { success: false, error: validation.error };
    }

    try {
      // Unregister existing hotkey if any
      if (this.currentHotkey) {
        this.unregisterHotkey(this.currentHotkey);
      }

      // Try to register the new hotkey
      const success = globalShortcut.register(config.accelerator, config.callback);

      if (!success) {
        return {
          success: false,
          error: 'Failed to register hotkey - it may already be in use by another application'
        };
      }

      // Store the registration
      this.registeredHotkeys.set(config.accelerator, config);
      this.currentHotkey = config.accelerator;

      console.log(`✅ Hotkey registered: ${config.accelerator}`);
      return { success: true };

    } catch (error) {
      console.error('Failed to register hotkey:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Register a press-and-hold hotkey with separate keydown/keyup callbacks
   */
  async registerPressHoldHotkey(config: PressHoldHotkeyConfig): Promise<{ success: boolean; error?: string }> {
    const validation = HotkeyManager.validateAccelerator(config.accelerator);
    if (!validation.valid) {
      return { success: false, error: validation.error };
    }

    try {
      // Unregister existing press-hold hotkey if any
      if (this.currentPressHoldHotkey) {
        this.unregisterPressHoldHotkey(this.currentPressHoldHotkey);
      }

      // Start uiohook if not already started
      if (!this.isUiohookStarted) {
        await this.startUiohook();
      }

      // Store the registration
      this.pressHoldHotkeys.set(config.accelerator, config);
      this.currentPressHoldHotkey = config.accelerator;

      console.log(`✅ Press-hold hotkey registered: ${config.accelerator}`);
      return { success: true };

    } catch (error) {
      console.error('Failed to register press-hold hotkey:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Start uiohook for low-level keyboard monitoring
   */
  private async startUiohook(): Promise<void> {
    if (this.isUiohookStarted) return;

    try {
      // Set up keyboard event handlers
      uIOhook.on('keydown', (event: UiohookKeyboardEvent) => {
        try {
          this.handleKeyDown(event);
        } catch (error) {
          console.error('Error handling keydown event:', error);
        }
      });

      uIOhook.on('keyup', (event: UiohookKeyboardEvent) => {
        try {
          this.handleKeyUp(event);
        } catch (error) {
          console.error('Error handling keyup event:', error);
        }
      });

      // Start the hook
      uIOhook.start();
      this.isUiohookStarted = true;
      console.log('✅ uiohook started for press-hold hotkey monitoring');
    } catch (error) {
      console.error('Failed to start uiohook:', error);
      throw new Error(`Failed to start low-level keyboard monitoring: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Handle keydown events from uiohook
   */
  private handleKeyDown(event: UiohookKeyboardEvent): void {
    // Only log the first keydown, not repeats
    if (!this.pressedKeys.has(event.keycode)) {
      console.log(`KeyDown: ${event.keycode} (${this.getKeyName(event.keycode)})`);
    }

    this.pressedKeys.add(event.keycode);
    this.updateModifiers();

    // Check if this matches any registered press-hold hotkey
    for (const [accelerator, config] of this.pressHoldHotkeys) {
      if (this.matchesAccelerator(accelerator, event)) {
        // Only trigger onKeyDown if this hotkey is not already active
        // This prevents multiple calls due to key repeat events
        if (!this.activeHotkeys.has(accelerator)) {
          console.log(`✅ KeyDown matched hotkey: ${accelerator}`);
          this.activeHotkeys.add(accelerator);
          config.onKeyDown();
        }
        break;
      }
    }
  }

  /**
   * Handle keyup events from uiohook
   */
  private handleKeyUp(event: UiohookKeyboardEvent): void {
    console.log(`KeyUp: ${event.keycode} (${this.getKeyName(event.keycode)})`);

    // Store the key before removing it for matching
    const wasPressed = this.pressedKeys.has(event.keycode);

    this.pressedKeys.delete(event.keycode);
    this.updateModifiers();

    // Check if this matches any registered press-hold hotkey
    // We need to check if this key was part of the active hotkey combination
    for (const [accelerator, config] of this.pressHoldHotkeys) {
      if (this.wasPartOfAccelerator(accelerator, event.keycode) && wasPressed && this.activeHotkeys.has(accelerator)) {
        console.log(`✅ KeyUp matched hotkey: ${accelerator}`);
        this.activeHotkeys.delete(accelerator); // Mark hotkey as no longer active
        config.onKeyUp();
        break;
      }
    }
  }

  /**
   * Update the current modifier state
   */
  private updateModifiers(): void {
    // Clear modifiers first
    this.currentModifiers.clear();

    // Check which modifiers are currently pressed
    if (this.pressedKeys.has(UiohookKey.Ctrl)) {
      this.currentModifiers.add('Control');
    }
    if (this.pressedKeys.has(UiohookKey.Shift)) {
      this.currentModifiers.add('Shift');
    }
    if (this.pressedKeys.has(UiohookKey.Alt)) {
      this.currentModifiers.add('Alt');
    }
    if (this.pressedKeys.has(UiohookKey.Meta)) {
      this.currentModifiers.add('Super');
    }
  }

  /**
   * Get a human-readable name for a key code
   */
  private getKeyName(keycode: number): string {
    for (const [name, code] of Object.entries(HotkeyManager.KEY_MAP)) {
      if (code === keycode) {
        return name;
      }
    }
    return `Unknown(${keycode})`;
  }

  /**
   * Check if a keycode was part of an accelerator combination
   */
  private wasPartOfAccelerator(accelerator: string, keycode: number): boolean {
    const parts = accelerator.split('+');

    // Check if it's a modifier key
    const modifierKeyCodes: number[] = [UiohookKey.Ctrl, UiohookKey.Shift, UiohookKey.Alt, UiohookKey.Meta];
    if (modifierKeyCodes.includes(keycode)) {
      return true; // Any modifier key release should trigger keyup
    }

    // Check if it's the main key
    for (const part of parts) {
      if (!['Control', 'CommandOrControl', 'Shift', 'Alt', 'Super'].includes(part)) {
        const expectedKeyCode = HotkeyManager.KEY_MAP[part];
        if (expectedKeyCode === keycode) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Check if the current key state matches an accelerator
   */
  private matchesAccelerator(accelerator: string, event: UiohookKeyboardEvent): boolean {
    const parts = accelerator.split('+');
    const requiredModifiers = new Set<string>();
    let mainKey: string | null = null;

    // Parse the accelerator
    for (const part of parts) {
      if (['Control', 'CommandOrControl', 'Shift', 'Alt', 'Super'].includes(part)) {
        if (part === 'CommandOrControl') {
          requiredModifiers.add(process.platform === 'darwin' ? 'Super' : 'Control');
        } else {
          requiredModifiers.add(part);
        }
      } else {
        mainKey = part;
      }
    }

    // Check if modifiers match
    if (requiredModifiers.size !== this.currentModifiers.size) {
      return false;
    }

    for (const modifier of requiredModifiers) {
      if (!this.currentModifiers.has(modifier)) {
        return false;
      }
    }

    // Check if main key matches
    if (mainKey) {
      const expectedKeyCode = HotkeyManager.KEY_MAP[mainKey];
      return expectedKeyCode === event.keycode;
    }

    return false;
  }

  /**
   * Unregister a specific hotkey
   */
  unregisterHotkey(accelerator: string): boolean {
    try {
      globalShortcut.unregister(accelerator);
      this.registeredHotkeys.delete(accelerator);

      if (this.currentHotkey === accelerator) {
        this.currentHotkey = null;
      }

      console.log(`🔓 Hotkey unregistered: ${accelerator}`);
      return true;
    } catch (error) {
      console.error('Failed to unregister hotkey:', error);
      return false;
    }
  }

  /**
   * Unregister a specific press-hold hotkey
   */
  unregisterPressHoldHotkey(accelerator: string): boolean {
    try {
      this.pressHoldHotkeys.delete(accelerator);

      if (this.currentPressHoldHotkey === accelerator) {
        this.currentPressHoldHotkey = null;
      }

      // Stop uiohook if no more press-hold hotkeys are registered
      if (this.pressHoldHotkeys.size === 0 && this.isUiohookStarted) {
        this.stopUiohook();
      }

      console.log(`🔓 Press-hold hotkey unregistered: ${accelerator}`);
      return true;
    } catch (error) {
      console.error('Failed to unregister press-hold hotkey:', error);
      return false;
    }
  }

  /**
   * Stop uiohook
   */
  private stopUiohook(): void {
    if (!this.isUiohookStarted) return;

    try {
      uIOhook.stop();
      this.isUiohookStarted = false;
      this.pressedKeys.clear();
      this.currentModifiers.clear();
      this.activeHotkeys.clear(); // Clear active hotkeys when stopping
      console.log('🔓 uiohook stopped');
    } catch (error) {
      console.error('Failed to stop uiohook:', error);
    }
  }

  /**
   * Unregister all hotkeys
   */
  unregisterAll(): void {
    try {
      globalShortcut.unregisterAll();
      this.registeredHotkeys.clear();
      this.currentHotkey = null;
      console.log('🔓 All hotkeys unregistered');
    } catch (error) {
      console.error('Failed to unregister all hotkeys:', error);
    }
  }

  /**
   * Unregister all press-hold hotkeys
   */
  unregisterAllPressHold(): void {
    try {
      this.pressHoldHotkeys.clear();
      this.currentPressHoldHotkey = null;

      if (this.isUiohookStarted) {
        this.stopUiohook();
      }

      console.log('🔓 All press-hold hotkeys unregistered');
    } catch (error) {
      console.error('Failed to unregister all press-hold hotkeys:', error);
    }
  }

  /**
   * Check if a hotkey is currently registered
   */
  isRegistered(accelerator: string): boolean {
    return globalShortcut.isRegistered(accelerator);
  }

  /**
   * Get the currently registered hotkey
   */
  getCurrentHotkey(): string | null {
    return this.currentHotkey;
  }

  /**
   * Get all registered hotkeys
   */
  getRegisteredHotkeys(): Map<string, HotkeyConfig> {
    return new Map(this.registeredHotkeys);
  }

  /**
   * Test if a hotkey can be registered (without actually registering it)
   */
  async testHotkey(accelerator: string): Promise<{ available: boolean; error?: string }> {
    const validation = HotkeyManager.validateAccelerator(accelerator);
    if (!validation.valid) {
      return { available: false, error: validation.error };
    }

    // Check if already registered by this app
    if (this.isRegistered(accelerator)) {
      return { available: false, error: 'Hotkey is already registered by this application' };
    }

    // Try to register temporarily to test availability
    try {
      const testCallback = () => {}; // Empty callback for testing
      const success = globalShortcut.register(accelerator, testCallback);
      
      if (success) {
        // Immediately unregister the test hotkey
        globalShortcut.unregister(accelerator);
        return { available: true };
      } else {
        return { available: false, error: 'Hotkey is already in use by another application' };
      }
    } catch (error) {
      return { 
        available: false, 
        error: error instanceof Error ? error.message : 'Unknown error occurred' 
      };
    }
  }

  /**
   * Get suggested default hotkeys that are likely to be available
   */
  static getDefaultHotkeySuggestions(): string[] {
    return [
      'CommandOrControl+Shift+Space',
      'CommandOrControl+Alt+Space',
      'F2',
      'F3',
      'F4',
      'CommandOrControl+Shift+V',
      'CommandOrControl+Shift+M',
      'Alt+Space',
    ];
  }

  /**
   * Get the currently registered press-hold hotkey
   */
  getCurrentPressHoldHotkey(): string | null {
    return this.currentPressHoldHotkey;
  }

  /**
   * Get all registered press-hold hotkeys
   */
  getRegisteredPressHoldHotkeys(): Map<string, PressHoldHotkeyConfig> {
    return new Map(this.pressHoldHotkeys);
  }

  /**
   * Cleanup - unregister all hotkeys
   */
  cleanup(): void {
    this.unregisterAll();
    this.unregisterAllPressHold();
  }
}
