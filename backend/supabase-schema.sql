-- Vibe Typer Backend Database Schema
-- This file contains the SQL schema for Supabase database setup

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create usage_logs table for tracking API usage
CREATE TABLE IF NOT EXISTS public.usage_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    endpoint VARCHAR(100) NOT NULL,
    tokens_used INTEGER,
    audio_duration DECIMAL(10,2), -- Duration in seconds
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_usage_logs_user_id ON public.usage_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_logs_created_at ON public.usage_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_usage_logs_endpoint ON public.usage_logs(endpoint);
CREATE INDEX IF NOT EXISTS idx_usage_logs_user_created ON public.usage_logs(user_id, created_at);

-- Create user_profiles table for additional user information (optional)
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    display_name VARCHAR(255),
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for user_profiles
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON public.user_profiles(user_id);

-- Row Level Security (RLS) policies

-- Enable RLS on usage_logs table
ALTER TABLE public.usage_logs ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own usage logs
DROP POLICY IF EXISTS "Users can view own usage logs" ON public.usage_logs;
CREATE POLICY "Users can view own usage logs" ON public.usage_logs
    FOR SELECT USING (auth.uid() = user_id);

-- Policy: Service role can insert usage logs
DROP POLICY IF EXISTS "Service role can insert usage logs" ON public.usage_logs;
CREATE POLICY "Service role can insert usage logs" ON public.usage_logs
    FOR INSERT WITH CHECK (true);

-- Policy: Service role can view all usage logs (for admin purposes)
DROP POLICY IF EXISTS "Service role can view all usage logs" ON public.usage_logs;
CREATE POLICY "Service role can view all usage logs" ON public.usage_logs
    FOR SELECT USING (auth.jwt() ->> 'role' = 'service_role');

-- Enable RLS on user_profiles table
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view and update their own profile
DROP POLICY IF EXISTS "Users can view own profile" ON public.user_profiles;
CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update own profile" ON public.user_profiles;
CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert own profile" ON public.user_profiles;
CREATE POLICY "Users can insert own profile" ON public.user_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Function to automatically create user profile when user signs up
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (user_id, display_name)
    VALUES (NEW.id, NEW.email);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create user profile on user creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update updated_at on user_profiles
DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON public.user_profiles;
CREATE TRIGGER update_user_profiles_updated_at
    BEFORE UPDATE ON public.user_profiles
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON public.usage_logs TO service_role;
GRANT ALL ON public.user_profiles TO service_role;
GRANT SELECT ON public.usage_logs TO authenticated;
GRANT ALL ON public.user_profiles TO authenticated;

-- Comments for documentation
COMMENT ON TABLE public.usage_logs IS 'Tracks API usage for billing and analytics';
COMMENT ON TABLE public.user_profiles IS 'Extended user profile information';
COMMENT ON COLUMN public.usage_logs.endpoint IS 'API endpoint that was called (e.g., transcribe, chat/completions)';
COMMENT ON COLUMN public.usage_logs.tokens_used IS 'Number of tokens consumed (for GPT requests)';
COMMENT ON COLUMN public.usage_logs.audio_duration IS 'Duration of audio processed in seconds (for Whisper requests)';

-- Create secure user_usage_summary view
-- This view provides usage statistics while respecting RLS policies
DROP VIEW IF EXISTS public.user_usage_summary;
CREATE VIEW public.user_usage_summary AS
SELECT 
    ul.user_id,
    up.display_name,
    COUNT(*) as total_requests,
    SUM(CASE WHEN ul.tokens_used IS NOT NULL THEN ul.tokens_used ELSE 0 END) as total_tokens,
    SUM(CASE WHEN ul.audio_duration IS NOT NULL THEN ul.audio_duration ELSE 0 END) as total_audio_duration,
    COUNT(CASE WHEN ul.endpoint = 'transcribe' THEN 1 END) as transcription_requests,
    COUNT(CASE WHEN ul.endpoint = 'chat/completions' THEN 1 END) as chat_requests,
    COUNT(CASE WHEN ul.endpoint = 'process-ai' THEN 1 END) as ai_processing_requests,
    MIN(ul.created_at) as first_request_at,
    MAX(ul.created_at) as last_request_at
FROM public.usage_logs ul
LEFT JOIN public.user_profiles up ON ul.user_id = up.user_id
GROUP BY ul.user_id, up.display_name;

-- Enable security_invoker to ensure the view respects RLS policies of underlying tables
ALTER VIEW public.user_usage_summary SET (security_invoker = true);

-- Grant appropriate permissions
GRANT SELECT ON public.user_usage_summary TO authenticated;
GRANT SELECT ON public.user_usage_summary TO service_role;

-- Ensure anon users cannot access the view
REVOKE ALL ON public.user_usage_summary FROM anon;

-- Insert some sample data for testing (optional - remove in production)
-- This will only work after you have created at least one user account
-- INSERT INTO public.usage_logs (user_id, endpoint, tokens_used, audio_duration) VALUES
--     ((SELECT id FROM auth.users LIMIT 1), 'transcribe', NULL, 15.5),
--     ((SELECT id FROM auth.users LIMIT 1), 'chat/completions', 150, NULL),
--     ((SELECT id FROM auth.users LIMIT 1), 'process-ai', 200, NULL);
