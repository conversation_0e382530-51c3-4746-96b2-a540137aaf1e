import { Request } from 'express';

// User types
export interface User {
  id: string;
  email: string;
  created_at: string;
  updated_at: string;
}

// Authentication types
export interface AuthenticatedRequest extends Request {
  user?: User;
}

// OpenAI API types
export interface TranscriptionOptions {
  language?: string;
  prompt?: string;
  temperature?: number;
  response_format?: 'json' | 'text' | 'srt' | 'verbose_json' | 'vtt';
}

export interface AIProcessingOptions {
  mode: 'write' | 'answer' | 'rewrite' | 'reply' | 'command';
  prompt: string;
  context?: string;
  selectedText?: string;
  clipboardContent?: string;
}

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface ChatCompletionRequest {
  model: string;
  messages: ChatMessage[];
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface TranscriptionResponse {
  text: string;
}

export interface ChatCompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: ChatMessage;
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// Usage tracking types
export interface UsageRecord {
  user_id: string;
  endpoint: string;
  tokens_used?: number;
  audio_duration?: number;
  created_at: string;
}

// Error types
export interface ApiError extends Error {
  statusCode?: number;
  code?: string;
}
