import axios, { AxiosResponse } from 'axios';
import FormData from 'form-data';
import { config } from '../config';
import { logger } from '../utils/logger';

export interface SonioxTranscriptionOptions {
  language?: string;
  context?: string;
  enable_speaker_diarization?: boolean;
  enable_language_identification?: boolean;
  client_reference_id?: string;
}

export interface SonioxTranscriptionResponse {
  id: string;
  status: 'queued' | 'processing' | 'completed' | 'error';
  created_at: string;
  model: string;
  audio_url?: string;
  file_id?: string;
  filename: string;
  language_hints?: string[];
  context?: string;
  audio_duration_ms?: number;
  error_type?: string;
  error_message?: string;
  client_reference_id?: string;
}

export interface SonioxTranscriptionResult {
  transcript: string;
  tokens: Array<{
    text: string;
    start_time_ms: number;
    end_time_ms: number;
    confidence: number;
    speaker?: string;
    language?: string;
  }>;
}

export interface SonioxFileUploadResponse {
  id: string;
  filename: string;
  size_bytes: number;
  created_at: string;
}

export class SonioxService {
  private apiKey: string;
  private baseUrl: string;
  private model: string;
  private timeout: number;
  private pollInterval: number;
  private maxPollAttempts: number;

  constructor() {
    this.apiKey = config.soniox.apiKey;
    this.baseUrl = config.soniox.baseUrl;
    this.model = config.soniox.model;
    this.timeout = config.soniox.timeout;
    this.pollInterval = config.soniox.pollInterval;
    this.maxPollAttempts = config.soniox.maxPollAttempts;
  }

  // Test API key validity
  async testApiKey(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
        timeout: 10000,
      });
      
      return response.status === 200;
    } catch (error) {
      logger.error('Soniox API key test failed:', error);
      return false;
    }
  }

  // Upload audio file to Soniox
  async uploadFile(audioBuffer: Buffer, filename: string = 'speech.wav'): Promise<string> {
    const startTime = Date.now();
    
    try {
      logger.info(`Uploading ${audioBuffer.length} bytes to Soniox as ${filename}`);

      const formData = new FormData();
      formData.append('file', audioBuffer, {
        filename,
        contentType: 'audio/wav',
      });

      const response: AxiosResponse<SonioxFileUploadResponse> = await axios.post(
        `${this.baseUrl}/files`,
        formData,
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            ...formData.getHeaders(),
          },
          timeout: this.timeout,
        }
      );

      const processingTime = Date.now() - startTime;
      logger.info('File upload successful', {
        fileId: response.data.id,
        filename: response.data.filename,
        sizeBytes: response.data.size_bytes,
        processingTime,
      });

      return response.data.id;
    } catch (error: any) {
      const processingTime = Date.now() - startTime;
      logger.error(`File upload failed after ${processingTime}ms:`, error);
      throw this.createAPIError(error, 'File upload');
    }
  }

  // Create transcription from uploaded file
  async createTranscription(
    fileId: string,
    options: SonioxTranscriptionOptions = {}
  ): Promise<SonioxTranscriptionResponse> {
    const startTime = Date.now();
    
    try {
      logger.info('Creating transcription', { fileId, options });

      const requestBody: any = {
        model: this.model,
        file_id: fileId,
      };

      // Add language hints if language is specified
      if (options.language) {
        requestBody.language_hints = [options.language];
      }

      // Add optional parameters
      if (options.context) {
        requestBody.context = options.context;
      }

      if (options.enable_speaker_diarization !== undefined) {
        requestBody.enable_speaker_diarization = options.enable_speaker_diarization;
      }

      if (options.enable_language_identification !== undefined) {
        requestBody.enable_language_identification = options.enable_language_identification;
      }

      if (options.client_reference_id) {
        requestBody.client_reference_id = options.client_reference_id;
      }

      const response: AxiosResponse<SonioxTranscriptionResponse> = await axios.post(
        `${this.baseUrl}/transcriptions`,
        requestBody,
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: this.timeout,
        }
      );

      const processingTime = Date.now() - startTime;
      logger.info('Transcription created', {
        transcriptionId: response.data.id,
        status: response.data.status,
        processingTime,
      });

      return response.data;
    } catch (error: any) {
      const processingTime = Date.now() - startTime;
      logger.error(`Transcription creation failed after ${processingTime}ms:`, error);
      throw this.createAPIError(error, 'Transcription creation');
    }
  }

  // Get transcription status
  async getTranscriptionStatus(transcriptionId: string): Promise<SonioxTranscriptionResponse> {
    try {
      const response: AxiosResponse<SonioxTranscriptionResponse> = await axios.get(
        `${this.baseUrl}/transcriptions/${transcriptionId}`,
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
          timeout: 10000, // Shorter timeout for status checks
        }
      );

      return response.data;
    } catch (error: any) {
      logger.error('Failed to get transcription status:', error);
      throw this.createAPIError(error, 'Status check');
    }
  }

  // Get transcription result
  async getTranscriptionResult(transcriptionId: string): Promise<SonioxTranscriptionResult> {
    try {
      const response: AxiosResponse<SonioxTranscriptionResult> = await axios.get(
        `${this.baseUrl}/transcriptions/${transcriptionId}/transcript`,
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
          },
          timeout: 10000,
        }
      );

      return response.data;
    } catch (error: any) {
      logger.error('Failed to get transcription result:', error);
      throw this.createAPIError(error, 'Result retrieval');
    }
  }

  // Poll for transcription completion
  async pollForCompletion(transcriptionId: string): Promise<SonioxTranscriptionResponse> {
    let attempts = 0;
    
    while (attempts < this.maxPollAttempts) {
      const status = await this.getTranscriptionStatus(transcriptionId);
      
      logger.info('Polling transcription status', {
        transcriptionId,
        status: status.status,
        attempt: attempts + 1,
        maxAttempts: this.maxPollAttempts,
      });

      if (status.status === 'completed') {
        return status;
      }

      if (status.status === 'error') {
        throw new Error(`Transcription failed: ${status.error_message || 'Unknown error'}`);
      }

      // Wait before next poll
      await new Promise(resolve => setTimeout(resolve, this.pollInterval));
      attempts++;
    }

    throw new Error(`Transcription timed out after ${this.maxPollAttempts} attempts`);
  }

  // Complete transcription workflow: upload, create, poll, and get result
  async transcribe(
    audioBuffer: Buffer,
    options: SonioxTranscriptionOptions = {}
  ): Promise<string> {
    const startTime = Date.now();
    
    try {
      logger.info(`Starting Soniox transcription for ${audioBuffer.length} bytes`);

      // Step 1: Upload file
      const fileId = await this.uploadFile(audioBuffer);

      // Step 2: Create transcription
      const transcription = await this.createTranscription(fileId, options);

      // Step 3: Poll for completion
      const completedTranscription = await this.pollForCompletion(transcription.id);

      // Step 4: Get result
      const result = await this.getTranscriptionResult(transcription.id);

      const processingTime = Date.now() - startTime;
      logger.info('Soniox transcription completed', {
        transcriptionId: transcription.id,
        transcriptLength: result.transcript.length,
        audioDurationMs: completedTranscription.audio_duration_ms,
        processingTime,
      });

      return result.transcript;
    } catch (error: any) {
      const processingTime = Date.now() - startTime;
      logger.error(`Soniox transcription failed after ${processingTime}ms:`, error);
      throw error;
    }
  }

  // Estimate audio duration from buffer size (for usage tracking)
  estimateAudioDuration(bufferSize: number): number {
    // Rough estimation: 16kHz * 2 bytes * 1 channel ≈ 32KB per second
    const bytesPerSecond = 32000;
    const headerSize = 44; // WAV header
    const audioDataSize = Math.max(0, bufferSize - headerSize);
    return audioDataSize / bytesPerSecond;
  }

  // Create standardized API error
  private createAPIError(error: any, context: string): Error {
    const apiError = new Error();
    
    if (error.response) {
      // Soniox API error
      const status = error.response.status;
      const data = error.response.data;
      
      apiError.message = `${context} failed: ${data.message || data.error || 'Unknown API error'}`;
      
      logger.error('Soniox API Error:', {
        status,
        error: data,
        context,
      });
    } else if (error.request) {
      // Network error
      apiError.message = `${context} failed: Network error`;
      logger.error('Soniox Network Error:', { context, error: error.message });
    } else {
      // Other error
      apiError.message = `${context} failed: ${error.message || String(error)}`;
      logger.error('Soniox Error:', { context, error: error.message });
    }
    
    return apiError;
  }
}

export const sonioxService = new SonioxService();
