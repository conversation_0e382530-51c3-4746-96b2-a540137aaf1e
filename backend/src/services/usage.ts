import { supabaseAdmin } from './supabase';
import { UsageRecord } from '../types';
import { logger } from '../utils/logger';

export class UsageService {
  // Track API usage
  async trackUsage(
    userId: string,
    endpoint: string,
    tokensUsed?: number,
    audioDuration?: number
  ): Promise<void> {
    try {
      const usageRecord: Omit<UsageRecord, 'created_at'> = {
        user_id: userId,
        endpoint,
        ...(tokensUsed !== undefined && { tokens_used: tokensUsed }),
        ...(audioDuration !== undefined && { audio_duration: audioDuration }),
      };

      const { error } = await supabaseAdmin
        .from('usage_logs')
        .insert([usageRecord]);

      if (error) {
        logger.error('Failed to track usage:', error);
        // Don't throw error - usage tracking shouldn't break the main functionality
      } else {
        logger.debug('Usage tracked:', usageRecord);
      }
    } catch (error) {
      logger.error('Error tracking usage:', error);
      // Don't throw error - usage tracking shouldn't break the main functionality
    }
  }

  // Get user usage statistics
  async getUserUsage(userId: string, days = 30): Promise<{
    totalRequests: number;
    totalTokens: number;
    totalAudioDuration: number;
    requestsByEndpoint: Record<string, number>;
  }> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data, error } = await supabaseAdmin
        .from('usage_logs')
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString());

      if (error) {
        logger.error('Failed to get user usage:', error);
        return {
          totalRequests: 0,
          totalTokens: 0,
          totalAudioDuration: 0,
          requestsByEndpoint: {},
        };
      }

      const usage = data || [];
      
      const stats = {
        totalRequests: usage.length,
        totalTokens: usage.reduce((sum, record) => sum + (record.tokens_used || 0), 0),
        totalAudioDuration: usage.reduce((sum, record) => sum + (record.audio_duration || 0), 0),
        requestsByEndpoint: {} as Record<string, number>,
      };

      // Count requests by endpoint
      usage.forEach(record => {
        stats.requestsByEndpoint[record.endpoint] = 
          (stats.requestsByEndpoint[record.endpoint] || 0) + 1;
      });

      return stats;
    } catch (error) {
      logger.error('Error getting user usage:', error);
      return {
        totalRequests: 0,
        totalTokens: 0,
        totalAudioDuration: 0,
        requestsByEndpoint: {},
      };
    }
  }

  // Get system-wide usage statistics (admin only)
  async getSystemUsage(days = 30): Promise<{
    totalUsers: number;
    totalRequests: number;
    totalTokens: number;
    totalAudioDuration: number;
    requestsByEndpoint: Record<string, number>;
    topUsers: Array<{ userId: string; requests: number; tokens: number }>;
  }> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data, error } = await supabaseAdmin
        .from('usage_logs')
        .select('*')
        .gte('created_at', startDate.toISOString());

      if (error) {
        logger.error('Failed to get system usage:', error);
        return {
          totalUsers: 0,
          totalRequests: 0,
          totalTokens: 0,
          totalAudioDuration: 0,
          requestsByEndpoint: {},
          topUsers: [],
        };
      }

      const usage = data || [];
      const uniqueUsers = new Set(usage.map(record => record.user_id));
      
      const stats = {
        totalUsers: uniqueUsers.size,
        totalRequests: usage.length,
        totalTokens: usage.reduce((sum, record) => sum + (record.tokens_used || 0), 0),
        totalAudioDuration: usage.reduce((sum, record) => sum + (record.audio_duration || 0), 0),
        requestsByEndpoint: {} as Record<string, number>,
        topUsers: [] as Array<{ userId: string; requests: number; tokens: number }>,
      };

      // Count requests by endpoint
      usage.forEach(record => {
        stats.requestsByEndpoint[record.endpoint] = 
          (stats.requestsByEndpoint[record.endpoint] || 0) + 1;
      });

      // Calculate top users
      const userStats = new Map<string, { requests: number; tokens: number }>();
      usage.forEach(record => {
        const current = userStats.get(record.user_id) || { requests: 0, tokens: 0 };
        userStats.set(record.user_id, {
          requests: current.requests + 1,
          tokens: current.tokens + (record.tokens_used || 0),
        });
      });

      stats.topUsers = Array.from(userStats.entries())
        .map(([userId, stats]) => ({ userId, ...stats }))
        .sort((a, b) => b.requests - a.requests)
        .slice(0, 10);

      return stats;
    } catch (error) {
      logger.error('Error getting system usage:', error);
      return {
        totalUsers: 0,
        totalRequests: 0,
        totalTokens: 0,
        totalAudioDuration: 0,
        requestsByEndpoint: {},
        topUsers: [],
      };
    }
  }
}

export const usageService = new UsageService();
