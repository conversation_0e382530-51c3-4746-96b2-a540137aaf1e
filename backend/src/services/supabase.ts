import { createClient, SupabaseClient, User } from '@supabase/supabase-js';
import { config } from '../config';
import { logger } from '../utils/logger';

// Create Supabase client with service role key for admin operations
export const supabaseAdmin = createClient(
  config.supabase.url,
  config.supabase.serviceRoleKey,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

// Create Supabase client with anon key for client operations
export const supabaseClient = createClient(
  config.supabase.url,
  config.supabase.anonKey
);

export class SupabaseService {
  private client: SupabaseClient;

  constructor(useServiceRole = false) {
    this.client = useServiceRole ? supabaseAdmin : supabaseClient;
  }

  // Verify JWT token and get user
  async verifyToken(token: string): Promise<User | null> {
    try {
      const { data: { user }, error } = await this.client.auth.getUser(token);
      
      if (error) {
        logger.warn('Token verification failed:', error.message);
        return null;
      }
      
      return user;
    } catch (error) {
      logger.error('Error verifying token:', error);
      return null;
    }
  }

  // Get user by ID
  async getUserById(userId: string): Promise<User | null> {
    try {
      const { data: { user }, error } = await supabaseAdmin.auth.admin.getUserById(userId);
      
      if (error) {
        logger.warn('Failed to get user by ID:', error.message);
        return null;
      }
      
      return user;
    } catch (error) {
      logger.error('Error getting user by ID:', error);
      return null;
    }
  }

  // Create user account
  async createUser(email: string, password: string): Promise<{ user: User | null; error: string | null }> {
    try {
      const { data, error } = await supabaseAdmin.auth.admin.createUser({
        email,
        password,
        email_confirm: true, // Auto-confirm email in development
      });
      
      if (error) {
        logger.warn('Failed to create user:', error.message);
        return { user: null, error: error.message };
      }
      
      logger.info('User created successfully:', { userId: data.user.id, email });
      return { user: data.user, error: null };
    } catch (error) {
      logger.error('Error creating user:', error);
      return { user: null, error: 'Failed to create user account' };
    }
  }

  // Sign in user
  async signInUser(email: string, password: string): Promise<{ user: User | null; session: any; error: string | null }> {
    try {
      const { data, error } = await this.client.auth.signInWithPassword({
        email,
        password,
      });
      
      if (error) {
        logger.warn('Sign in failed:', error.message);
        return { user: null, session: null, error: error.message };
      }
      
      logger.info('User signed in successfully:', { userId: data.user?.id, email });
      return { user: data.user, session: data.session, error: null };
    } catch (error) {
      logger.error('Error signing in user:', error);
      return { user: null, session: null, error: 'Failed to sign in' };
    }
  }

  // Sign out user
  async signOutUser(token: string): Promise<{ error: string | null }> {
    try {
      const { error } = await this.client.auth.signOut();
      
      if (error) {
        logger.warn('Sign out failed:', error.message);
        return { error: error.message };
      }
      
      logger.info('User signed out successfully');
      return { error: null };
    } catch (error) {
      logger.error('Error signing out user:', error);
      return { error: 'Failed to sign out' };
    }
  }

  // Refresh access token
  async refreshToken(refreshToken: string): Promise<{ session: any; error: string | null }> {
    try {
      const { data, error } = await this.client.auth.refreshSession({
        refresh_token: refreshToken,
      });
      
      if (error) {
        logger.warn('Token refresh failed:', error.message);
        return { session: null, error: error.message };
      }
      
      return { session: data.session, error: null };
    } catch (error) {
      logger.error('Error refreshing token:', error);
      return { session: null, error: 'Failed to refresh token' };
    }
  }

  // Reset password
  async resetPassword(email: string): Promise<{ error: string | null }> {
    try {
      const { error } = await this.client.auth.resetPasswordForEmail(email, {
        redirectTo: `${config.supabase.url}/auth/v1/verify`,
      });
      
      if (error) {
        logger.warn('Password reset failed:', error.message);
        return { error: error.message };
      }
      
      logger.info('Password reset email sent:', { email });
      return { error: null };
    } catch (error) {
      logger.error('Error sending password reset:', error);
      return { error: 'Failed to send password reset email' };
    }
  }
}
