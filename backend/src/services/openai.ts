import axios, { AxiosResponse } from 'axios';
import FormData from 'form-data';
import { config } from '../config';
import { logger } from '../utils/logger';
import {
  TranscriptionOptions,
  ChatCompletionRequest,
  ChatCompletionResponse,
  TranscriptionResponse,
  ApiError
} from '../types';
import { sonioxService, SonioxTranscriptionOptions } from './soniox';

export class OpenAIService {
  private apiKey: string;
  private chatUrl: string;
  private modelsUrl: string;
  private timeout: number;

  constructor() {
    this.apiKey = config.openai.apiKey;
    this.chatUrl = config.openai.chatUrl;
    this.modelsUrl = config.openai.modelsUrl;
    this.timeout = config.openai.timeout;
  }

  // Test API key validity
  async testApiKey(): Promise<boolean> {
    try {
      const response = await axios.get(this.modelsUrl, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
        timeout: 10000,
      });
      
      return response.status === 200;
    } catch (error) {
      logger.error('OpenAI API key test failed:', error);
      return false;
    }
  }



  // Transcribe audio using Soniox API (replacing OpenAI Whisper)
  async transcribe(
    audioBuffer: Buffer,
    options: TranscriptionOptions = {}
  ): Promise<string> {
    const startTime = Date.now();

    try {
      logger.info(`Transcribing ${audioBuffer.length} bytes of audio data using Soniox`);

      // Convert OpenAI TranscriptionOptions to Soniox format
      const sonioxOptions: SonioxTranscriptionOptions = {};

      if (options.language) {
        sonioxOptions.language = options.language;
      }

      // Map OpenAI prompt to Soniox context
      if (options.prompt) {
        sonioxOptions.context = options.prompt;
      }

      // Use Soniox service for transcription
      const transcript = await sonioxService.transcribe(audioBuffer, sonioxOptions);

      const processingTime = Date.now() - startTime;
      const audioDuration = this.estimateAudioDuration(audioBuffer.length);

      logger.info(`Soniox transcription successful: ${processingTime}ms for ${audioDuration.toFixed(1)}s audio`);

      return transcript.trim();
    } catch (error: any) {
      const processingTime = Date.now() - startTime;
      logger.error(`Soniox transcription failed after ${processingTime}ms:`, error);
      throw this.createAPIError(error, 'Transcription');
    }
  }

  // Generate chat completion using GPT API
  async chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    const startTime = Date.now();
    
    try {
      logger.info('Making chat completion request', {
        model: request.model,
        messageCount: request.messages.length,
        maxTokens: request.max_tokens,
      });

      // Performance optimization: Calculate timeout based on expected response length
      const estimatedTokens = request.max_tokens || 1000;
      const baseTimeout = 10000; // 10 seconds base
      const tokenTimeout = estimatedTokens * 5; // 5ms per token (conservative estimate)
      const adaptiveTimeout = Math.min(baseTimeout + tokenTimeout, 45000); // Cap at 45s

      const response: AxiosResponse<ChatCompletionResponse> = await axios.post(
        this.chatUrl,
        request,
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: adaptiveTimeout,
        }
      );

      if (!response.data) {
        throw new Error('No response data received');
      }

      const processingTime = Date.now() - startTime;
      const tokensGenerated = response.data.usage?.completion_tokens || 0;

      logger.info('Chat completion successful', {
        id: response.data.id,
        model: response.data.model,
        usage: response.data.usage,
        processingTime,
        tokensPerSecond: tokensGenerated > 0 ? (tokensGenerated * 1000 / processingTime).toFixed(1) : 0
      });

      return response.data;
    } catch (error: any) {
      const processingTime = Date.now() - startTime;
      logger.error(`Chat completion failed after ${processingTime}ms:`, error);
      throw this.createAPIError(error, 'Chat completion');
    }
  }

  // Validate WAV buffer format (simplified version for cases where needed)
  private isValidWAVBuffer(buffer: Buffer): boolean {
    if (buffer.length < 44) {
      logger.warn('Audio buffer too small for WAV format (< 44 bytes)');
      return false;
    }

    // Quick validation: just check RIFF header
    const riffHeader = buffer.slice(0, 4).toString('ascii');
    if (riffHeader !== 'RIFF') {
      logger.warn(`Invalid WAV header: Expected 'RIFF', got '${riffHeader}'`);
      return false;
    }

    logger.debug('Valid WAV format detected');
    return true;
  }

  // Create standardized API error with performance context
  private createAPIError(error: any, context: string): ApiError {
    const apiError = new Error() as ApiError;
    
    if (error.response) {
      // OpenAI API error
      const status = error.response.status;
      const data = error.response.data;
      
      apiError.message = `${context} failed: ${data.error?.message || 'Unknown API error'}`;
      apiError.statusCode = status;
      apiError.code = data.error?.code || 'api_error';
      
      logger.error('OpenAI API Error:', {
        status,
        error: data.error,
        context,
      });
    } else if (error.request) {
      // Network error
      apiError.message = `${context} failed: Unable to reach OpenAI API`;
      apiError.statusCode = 503;
      apiError.code = 'network_error';
    } else if (error.code === 'ECONNABORTED') {
      // Timeout error
      apiError.message = `${context} timed out: Request took too long to complete`;
      apiError.statusCode = 408;
      apiError.code = 'timeout_error';
    } else {
      // Other error
      apiError.message = `${context} failed: ${error.message || String(error)}`;
      apiError.statusCode = 500;
      apiError.code = 'internal_error';
    }
    
    return apiError;
  }

  // Get estimated token count for text (rough approximation)
  estimateTokenCount(text: string): number {
    // Rough approximation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
  }

  // Get estimated audio duration from buffer size (delegated to Soniox service)
  estimateAudioDuration(bufferSize: number): number {
    return sonioxService.estimateAudioDuration(bufferSize);
  }
}

export const openaiService = new OpenAIService();

// Helper function to generate AI prompts (matching client-side logic)
export function generateAIPrompts(
  mode: string,
  prompt: string,
  text: string,
  selectedText?: string,
  clipboardContent?: string
): { systemPrompt: string; userPrompt: string } {
  const contextContent = selectedText || clipboardContent || text;

  const prompts = {
    write: {
      system: 'You are a helpful writing assistant. Generate clear, well-written text based on the user\'s request. If the user mentions "clipboard" or "selected text", use the provided content as context.',
      user: shouldUseContext(mode, prompt)
        ? `Write: ${prompt}\n\n${getContextLabel(selectedText)} for context: "${contextContent}"`
        : `Write: ${prompt}`
    },
    answer: {
      system: 'You are a knowledgeable assistant. Provide accurate, helpful answers to questions. Be concise but thorough.',
      user: `Answer the question: ${prompt}`
    },
    rewrite: {
      system: 'You are a text editing assistant. Rewrite the provided text according to the user\'s instructions. Maintain the original meaning unless specifically asked to change it.',
      user: `Rewrite the following text according to these instructions: "${prompt}"\n\nText to rewrite: "${contextContent}"`
    },
    reply: {
      system: 'You are a communication assistant. Generate an appropriate reply to the provided message or email. Match the tone and context of the original message.',
      user: `Generate a reply to this message: "${contextContent}"\n\nReply style: ${prompt}`
    },
    command: {
      system: 'You are a system command assistant. Interpret the user\'s request and suggest appropriate commands or actions.',
      user: `Execute or suggest command for: ${prompt}`
    }
  };

  const modePrompts = prompts[mode as keyof typeof prompts];
  if (!modePrompts) {
    throw new Error(`Unsupported AI mode: ${mode}`);
  }

  return {
    systemPrompt: modePrompts.system,
    userPrompt: modePrompts.user
  };
}

function shouldUseContext(mode: string, prompt: string): boolean {
  return mode === 'write' && (prompt.toLowerCase().includes('clipboard') || prompt.toLowerCase().includes('selected'));
}

function getContextLabel(selectedText?: string): string {
  return selectedText ? 'Selected text' : 'Clipboard content';
}
