import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export const config = {
  // Server configuration
  port: parseInt(process.env.PORT || '3001', 10),
  nodeEnv: process.env.NODE_ENV || 'development',
  
  // OpenAI configuration (for chat completions only)
  openai: {
    apiKey: process.env.OPENAI_API_KEY || '',
    chatUrl: 'https://api.openai.com/v1/chat/completions',
    modelsUrl: 'https://api.openai.com/v1/models',
    timeout: 30000, // 30 seconds
  },

  // Soniox configuration (for speech transcription)
  soniox: {
    apiKey: process.env.SONIOX_API_KEY || '',
    baseUrl: 'https://api.soniox.com/v1',
    model: 'stt-async-preview', // Default model for async transcription
    timeout: 60000, // 60 seconds for async operations
    pollInterval: 2000, // 2 seconds between status polls
    maxPollAttempts: 150, // Max 5 minutes of polling (150 * 2s)
  },
  
  // Supabase configuration
  supabase: {
    url: process.env.SUPABASE_URL || '',
    anonKey: process.env.SUPABASE_ANON_KEY || '',
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  },
  
  // JWT configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'fallback-secret-change-in-production',
  },
  
  // Rate limiting configuration
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),
  },
  
  // Logging configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
  },
  
  // CORS configuration
  cors: {
    origin: process.env.NODE_ENV === 'production' 
      ? ['https://your-domain.com'] // Update with your production domain
      : ['http://localhost:*', 'file://*'], // Allow Electron in development
    credentials: true,
  },
};

// Validation
export function validateConfig(): void {
  const required = [
    'OPENAI_API_KEY',
    'SONIOX_API_KEY',
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
  ];

  const missing = required.filter(key => !process.env[key]);

  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  if (!config.openai.apiKey.startsWith('sk-')) {
    throw new Error('Invalid OpenAI API key format');
  }

  if (!config.soniox.apiKey) {
    throw new Error('Soniox API key is required');
  }

  if (!config.supabase.url.startsWith('https://')) {
    throw new Error('Invalid Supabase URL format');
  }
}
