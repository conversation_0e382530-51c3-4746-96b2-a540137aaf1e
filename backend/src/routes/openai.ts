import { Router, Request, Response } from 'express';
import multer from 'multer';
import { authenticateUser } from '../middleware/auth';
import { openaiService, generateAIPrompts } from '../services/openai';
import { usageService } from '../services/usage';
import { AuthenticatedRequest, TranscriptionOptions, ChatCompletionRequest } from '../types';
import { logger } from '../utils/logger';

const router = Router();

// Configure multer for file uploads (audio files)
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 25 * 1024 * 1024, // 25MB limit (OpenAI's limit)
  },
  fileFilter: (req, file, cb) => {
    // Accept audio files
    if (file.mimetype.startsWith('audio/') || file.mimetype === 'application/octet-stream') {
      cb(null, true);
    } else {
      cb(new Error('Only audio files are allowed'));
    }
  },
});

// Test OpenAI API connectivity
router.get('/test', authenticateUser, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const isValid = await openaiService.testApiKey();
    
    return res.json({
      success: true,
      data: {
        apiKeyValid: isValid,
        message: isValid ? 'OpenAI API is accessible' : 'OpenAI API is not accessible',
      },
    });
  } catch (error) {
    logger.error('API test failed:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to test OpenAI API',
    });
  }
});

// Transcribe audio using Whisper API
router.post('/transcribe', authenticateUser, upload.single('file'), async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'Audio file is required',
      });
    }

    const audioBuffer = req.file.buffer;
    const options: TranscriptionOptions = {};

    // Extract transcription options from form data or body
    if (req.body.language) {
      options.language = req.body.language;
    }
    if (req.body.prompt) {
      options.prompt = req.body.prompt;
    }
    if (req.body.temperature !== undefined) {
      options.temperature = parseFloat(req.body.temperature);
    }
    if (req.body.response_format) {
      options.response_format = req.body.response_format;
    }

    logger.info('Transcription request received', {
      userId: req.user?.id,
      fileSize: audioBuffer.length,
      options,
    });

    // Transcribe audio
    const transcription = await openaiService.transcribe(audioBuffer, options);

    // Track usage
    const audioDuration = openaiService.estimateAudioDuration(audioBuffer.length);
    await usageService.trackUsage(
      req.user!.id,
      'transcribe',
      undefined, // No tokens for transcription
      audioDuration
    );

    logger.info('Transcription completed', {
      userId: req.user?.id,
      transcriptionLength: transcription.length,
      audioDuration,
    });

    // Return transcription in the same format as OpenAI API
    if (options.response_format === 'json' || options.response_format === 'verbose_json') {
      return res.json({
        text: transcription,
      });
    } else {
      return res.send(transcription);
    }
  } catch (error: any) {
    logger.error('Transcription failed:', error);
    return res.status(error.statusCode || 500).json({
      success: false,
      error: error.message || 'Transcription failed',
    });
  }
});

// Chat completion using GPT API
router.post('/chat/completions', authenticateUser, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const chatRequest: ChatCompletionRequest = req.body;

    // Validation
    if (!chatRequest.model) {
      return res.status(400).json({
        success: false,
        error: 'Model is required',
      });
    }

    if (!chatRequest.messages || !Array.isArray(chatRequest.messages) || chatRequest.messages.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Messages array is required and must not be empty',
      });
    }

    logger.info('Chat completion request received', {
      userId: req.user?.id,
      model: chatRequest.model,
      messageCount: chatRequest.messages.length,
      maxTokens: chatRequest.max_tokens,
    });

    // Make chat completion request
    const response = await openaiService.chatCompletion(chatRequest);

    // Track usage
    const tokensUsed = response.usage?.total_tokens || 0;
    await usageService.trackUsage(
      req.user!.id,
      'chat/completions',
      tokensUsed
    );

    logger.info('Chat completion completed', {
      userId: req.user?.id,
      responseId: response.id,
      tokensUsed,
    });

    // Return response in OpenAI format
    return res.json(response);
  } catch (error: any) {
    logger.error('Chat completion failed:', error);
    return res.status(error.statusCode || 500).json({
      success: false,
      error: error.message || 'Chat completion failed',
    });
  }
});

// AI processing endpoint (matches client-side processWithAI method)
router.post('/process-ai', authenticateUser, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { text, options } = req.body;

    // Validation
    if (!text || !options) {
      return res.status(400).json({
        success: false,
        error: 'Text and options are required',
      });
    }

    if (!options.mode || !options.prompt) {
      return res.status(400).json({
        success: false,
        error: 'Mode and prompt are required in options',
      });
    }

    logger.info('AI processing request received', {
      userId: req.user?.id,
      mode: options.mode,
      textLength: text.length,
    });

    // Generate prompts using the same logic as the client
    const { systemPrompt, userPrompt } = generateAIPrompts(
      options.mode,
      options.prompt,
      text,
      options.selectedText,
      options.clipboardContent
    );

    // Create chat completion request
    const chatRequest: ChatCompletionRequest = {
      model: 'gpt-4o-mini', // Use a cost-effective model
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt },
      ],
      max_tokens: 1000,
      temperature: 0.7,
    };

    // Make chat completion request
    const response = await openaiService.chatCompletion(chatRequest);
    const content = response.choices[0]?.message?.content?.trim() || null;

    // Track usage
    const tokensUsed = response.usage?.total_tokens || 0;
    await usageService.trackUsage(
      req.user!.id,
      'process-ai',
      tokensUsed
    );

    logger.info('AI processing completed', {
      userId: req.user?.id,
      mode: options.mode,
      tokensUsed,
      responseLength: content?.length || 0,
    });

    return res.json({
      success: true,
      data: {
        response: content,
        usage: response.usage,
      },
    });
  } catch (error: any) {
    logger.error('AI processing failed:', error);
    return res.status(error.statusCode || 500).json({
      success: false,
      error: error.message || 'AI processing failed',
    });
  }
});

// Get user usage statistics
router.get('/usage', authenticateUser, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const days = parseInt(req.query.days as string) || 30;
    const usage = await usageService.getUserUsage(req.user!.id, days);

    return res.json({
      success: true,
      data: usage,
    });
  } catch (error) {
    logger.error('Failed to get usage statistics:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to get usage statistics',
    });
  }
});

export { router as openaiRoutes };
