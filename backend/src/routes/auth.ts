import { Router, Request, Response } from 'express';
import { SupabaseService } from '../services/supabase';
import { authenticateUser } from '../middleware/auth';
import { AuthenticatedRequest } from '../types';
import { logger } from '../utils/logger';

const router = Router();
const supabaseService = new SupabaseService();

// Sign up endpoint
router.post('/signup', async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;
    
    // Validation
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Email and password are required',
      });
    }
    
    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        error: 'Password must be at least 6 characters long',
      });
    }
    
    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid email format',
      });
    }
    
    // Create user
    const { user, error } = await supabaseService.createUser(email, password);
    
    if (error) {
      return res.status(400).json({
        success: false,
        error,
      });
    }
    
    return res.status(201).json({
      success: true,
      message: 'Account created successfully',
      data: {
        user: {
          id: user?.id,
          email: user?.email,
          created_at: user?.created_at,
        },
      },
    });
  } catch (error) {
    logger.error('Signup error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to create account',
    });
  }
});

// Sign in endpoint
router.post('/signin', async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;
    
    // Validation
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Email and password are required',
      });
    }
    
    // Sign in user
    const { user, session, error } = await supabaseService.signInUser(email, password);
    
    if (error) {
      return res.status(401).json({
        success: false,
        error,
      });
    }
    
    return res.json({
      success: true,
      message: 'Signed in successfully',
      data: {
        user: {
          id: user?.id,
          email: user?.email,
          created_at: user?.created_at,
        },
        session: {
          access_token: session?.access_token,
          refresh_token: session?.refresh_token,
          expires_at: session?.expires_at,
        },
      },
    });
  } catch (error) {
    logger.error('Signin error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to sign in',
    });
  }
});

// Sign out endpoint
router.post('/signout', authenticateUser, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '') || '';
    
    const { error } = await supabaseService.signOutUser(token);
    
    if (error) {
      return res.status(400).json({
        success: false,
        error,
      });
    }
    
    return res.json({
      success: true,
      message: 'Signed out successfully',
    });
  } catch (error) {
    logger.error('Signout error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to sign out',
    });
  }
});

// Refresh token endpoint
router.post('/refresh', async (req: Request, res: Response) => {
  try {
    const { refresh_token } = req.body;
    
    if (!refresh_token) {
      return res.status(400).json({
        success: false,
        error: 'Refresh token is required',
      });
    }
    
    const { session, error } = await supabaseService.refreshToken(refresh_token);
    
    if (error) {
      return res.status(401).json({
        success: false,
        error,
      });
    }
    
    return res.json({
      success: true,
      message: 'Token refreshed successfully',
      data: {
        session: {
          access_token: session?.access_token,
          refresh_token: session?.refresh_token,
          expires_at: session?.expires_at,
        },
      },
    });
  } catch (error) {
    logger.error('Token refresh error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to refresh token',
    });
  }
});

// Get current user endpoint
router.get('/me', authenticateUser, (req: AuthenticatedRequest, res: Response) => {
  res.json({
    success: true,
    data: {
      user: req.user,
    },
  });
});

// Reset password endpoint
router.post('/reset-password', async (req: Request, res: Response) => {
  try {
    const { email } = req.body;
    
    if (!email) {
      return res.status(400).json({
        success: false,
        error: 'Email is required',
      });
    }
    
    const { error } = await supabaseService.resetPassword(email);
    
    if (error) {
      return res.status(400).json({
        success: false,
        error,
      });
    }
    
    return res.json({
      success: true,
      message: 'Password reset email sent',
    });
  } catch (error) {
    logger.error('Password reset error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to send password reset email',
    });
  }
});

export { router as authRoutes };
