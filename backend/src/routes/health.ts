import { Router, Request, Response } from 'express';
import { config } from '../config';
import { logger } from '../utils/logger';

const router = Router();

// Basic health check
router.get('/', (req: Request, res: Response) => {
  res.json({
    success: true,
    message: 'Vibe Typer Backend is healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: config.nodeEnv,
  });
});

// Detailed health check
router.get('/detailed', async (req: Request, res: Response) => {
  const healthCheck = {
    success: true,
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: config.nodeEnv,
    services: {
      openai: false,
      supabase: false,
    },
    uptime: process.uptime(),
    memory: process.memoryUsage(),
  };

  try {
    // Check OpenAI API connectivity
    const axios = require('axios');
    const openaiResponse = await axios.get(config.openai.modelsUrl, {
      headers: {
        'Authorization': `Bearer ${config.openai.apiKey}`,
      },
      timeout: 5000,
    });
    healthCheck.services.openai = openaiResponse.status === 200;
  } catch (error) {
    logger.warn('OpenAI health check failed:', error);
    healthCheck.services.openai = false;
  }

  try {
    // Check Supabase connectivity
    const { createClient } = require('@supabase/supabase-js');
    const supabase = createClient(config.supabase.url, config.supabase.anonKey);
    
    // Simple query to test connection
    const { error } = await supabase.from('auth.users').select('count').limit(1);
    healthCheck.services.supabase = !error;
  } catch (error) {
    logger.warn('Supabase health check failed:', error);
    healthCheck.services.supabase = false;
  }

  // Overall health status
  const allServicesHealthy = Object.values(healthCheck.services).every(status => status);
  healthCheck.success = allServicesHealthy;

  const statusCode = allServicesHealthy ? 200 : 503;
  res.status(statusCode).json(healthCheck);
});

export { router as healthRoutes };
