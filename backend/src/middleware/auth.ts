import { Request, Response, NextFunction } from 'express';
import { SupabaseService } from '../services/supabase';
import { AuthenticatedRequest, User } from '../types';
import { logger } from '../utils/logger';

const supabaseService = new SupabaseService();

// Extract token from Authorization header
function extractToken(req: Request): string | null {
  const authHeader = req.headers.authorization;
  
  if (!authHeader) {
    return null;
  }
  
  // Support both "Bearer token" and "token" formats
  if (authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  return authHeader;
}

// Authentication middleware
export async function authenticateUser(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const token = extractToken(req);
    
    if (!token) {
      res.status(401).json({
        success: false,
        error: 'Authentication token required',
      });
      return;
    }
    
    // Verify token with Supabase
    const user = await supabaseService.verifyToken(token);
    
    if (!user) {
      res.status(401).json({
        success: false,
        error: 'Invalid or expired authentication token',
      });
      return;
    }
    
    // Add user to request object
    req.user = {
      id: user.id,
      email: user.email || '',
      created_at: user.created_at,
      updated_at: user.updated_at || user.created_at,
    };
    
    logger.debug('User authenticated:', { userId: user.id, email: user.email });
    next();
  } catch (error) {
    logger.error('Authentication error:', error);
    res.status(500).json({
      success: false,
      error: 'Authentication failed',
    });
  }
}

// Optional authentication middleware (doesn't fail if no token)
export async function optionalAuth(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const token = extractToken(req);
    
    if (token) {
      const user = await supabaseService.verifyToken(token);
      
      if (user) {
        req.user = {
          id: user.id,
          email: user.email || '',
          created_at: user.created_at,
          updated_at: user.updated_at || user.created_at,
        };
      }
    }
    
    next();
  } catch (error) {
    logger.warn('Optional authentication failed:', error);
    // Continue without authentication
    next();
  }
}

// Middleware to check if user is authenticated
export function requireAuth(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void {
  if (!req.user) {
    res.status(401).json({
      success: false,
      error: 'Authentication required',
    });
    return;
  }
  
  next();
}
