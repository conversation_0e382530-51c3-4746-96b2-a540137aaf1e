{"name": "vibe-typer-backend", "version": "1.0.0", "description": "Backend service for Vibe Typer - OpenAI API proxy with Supabase authentication", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "ts-node src/server.ts", "dev:watch": "nodemon --exec ts-node src/server.ts", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["speech-to-text", "openai", "whisper", "gpt", "proxy", "supabase", "authentication"], "author": "Your Name", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.39.0", "axios": "^1.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "form-data": "^4.0.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "winston": "^3.11.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.9.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}}