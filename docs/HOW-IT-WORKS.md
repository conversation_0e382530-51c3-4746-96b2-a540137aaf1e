# How the New Vibe Typer Backend Works

A detailed technical explanation of the new architecture and how everything connects together.

## Overview: What Changed

### Before (Old System)
```
User's Computer
├── Vibe Typer App
├── User enters their OpenAI API key
└── App makes direct calls to OpenAI API
```

### After (New System)
```
User's Computer                    Your Server                 External Services
├── Vibe Typer App        →       ├── Backend Service    →    ├── OpenAI API
├── User creates account          ├── Authentication           └── (Whisper & GPT)
└── App talks to YOUR backend     └── Usage tracking      →    ├── Supabase Database
                                                               └── (User accounts & logs)
```

## The Complete Flow: Step by Step

### 1. User Opens the App

**What happens:**
- App starts up on user's computer
- A<PERSON> tries to connect to YOUR backend server
- App checks if user is already signed in

**Technical details:**
- App looks for stored authentication tokens
- Makes a request to `https://your-backend-domain.com/api/auth/me`
- If no valid token, shows sign-in screen

### 2. User Creates Account / Signs In

**What happens:**
- User enters email and password in the app
- App sends credentials to YOUR backend
- Backend talks to Supabase to verify/create account
- Backend sends back authentication token
- App stores token for future requests

**The actual network calls:**
```
User's App  →  Your Backend  →  Supabase
POST /api/auth/signin
{
  "email": "<EMAIL>",
  "password": "userpassword"
}
                ↓
            Validates with Supabase
                ↓
            Returns JWT token
                ↓
App stores token locally
```

### 3. User Records Voice

**What happens:**
- User presses hotkey and speaks
- App records audio on their computer
- App converts audio to WAV format
- App sends audio file to YOUR backend (with auth token)

**Technical details:**
- Audio is recorded using the computer's microphone
- Audio is packaged as a file upload (multipart/form-data)
- Request includes the user's authentication token in headers

### 4. Backend Processes Voice

**What happens:**
- YOUR backend receives the audio file
- Backend checks the user's authentication token
- Backend uploads audio to Soniox API and creates transcription
- Backend polls Soniox API until transcription is complete
- Soniox returns transcribed text
- Backend logs the usage for the user
- Backend sends text back to user's app

**The network flow:**
```
User's App  →  Your Backend  →  Soniox API
POST /api/openai/transcribe
(with audio file + auth token)
                ↓
            Validates user token
                ↓
            Uploads file to Soniox
                ↓
            Creates transcription job
                ↓
            Polls for completion
                ↓
            Gets transcribed text
                ↓
            Logs usage in database
                ↓
            Returns text to app
```

### 5. App Types the Text

**What happens:**
- App receives transcribed text from YOUR backend
- App simulates keyboard typing to insert text
- Text appears where user's cursor was

## Network Architecture Details

### How Apps Connect to Your Backend

**The URL Structure:**
- Your backend will have a domain like: `https://vibe-typer-backend.railway.app`
- Or your own domain: `https://api.vibetyper.com`
- The app is configured to make requests to this URL

**Where this is configured:**
In the client app code (`src/config/manager.ts`):
```typescript
private defaultConfig: AppConfig = {
  authConfig: {
    backendUrl: 'https://your-backend-domain.com',  // ← This URL
    // ...
  }
}
```

### DNS and Domain Setup

**Option 1: Use hosting platform's domain**
- Railway gives you: `https://your-app-name.up.railway.app`
- Heroku gives you: `https://your-app-name.herokuapp.com`
- No DNS setup needed - works immediately

**Option 2: Use your own domain**
- Buy a domain (like `api.vibetyper.com`)
- Point it to your hosting platform
- Set up DNS A record or CNAME
- Configure SSL certificate (usually automatic)

**Example DNS setup:**
```
Type: CNAME
Name: api
Value: your-app-name.up.railway.app
```

### Authentication Flow Details

**How tokens work:**
1. User signs in → Backend creates JWT token
2. App stores token locally (encrypted)
3. Every API request includes: `Authorization: Bearer <token>`
4. Backend validates token on every request
5. Token expires after set time (usually 1 hour)
6. App automatically refreshes expired tokens

**What's in the token:**
```json
{
  "user_id": "uuid-of-user",
  "email": "<EMAIL>",
  "exp": **********,  // expiration time
  "iat": **********   // issued at time
}
```

## Database Structure

### What's Stored in Supabase

**User accounts (handled by Supabase auth):**
- Email addresses
- Encrypted passwords
- User IDs
- Account creation dates

**Usage tracking (your custom table):**
```sql
usage_logs table:
- user_id (who made the request)
- endpoint (transcribe, chat, etc.)
- tokens_used (for AI requests)
- audio_duration (for voice requests)
- created_at (when it happened)
```

**Why track usage:**
- Monitor costs (OpenAI charges per use)
- Prepare for future billing/subscriptions
- Understand user behavior
- Detect abuse or unusual patterns

## API Endpoints Explained

### Authentication Endpoints

**POST /api/auth/signup**
- Creates new user account
- Stores in Supabase
- Returns success/error

**POST /api/auth/signin**
- Validates credentials
- Returns JWT token
- App uses token for future requests

**GET /api/auth/me**
- Checks if current token is valid
- Returns user info if authenticated

### OpenAI Proxy Endpoints

**POST /api/openai/transcribe**
- Receives audio file from app
- Uploads to Soniox API and creates async transcription
- Polls Soniox API until transcription completes
- Returns transcribed text
- Logs usage in database

**POST /api/openai/process-ai**
- Receives text and AI command from app
- Builds prompt for GPT
- Forwards to OpenAI GPT API
- Returns AI-generated response
- Logs token usage

**GET /api/openai/usage**
- Returns user's usage statistics
- Shows requests made, tokens used, etc.

## Security Details

### How Data is Protected

**In transit:**
- All connections use HTTPS (encrypted)
- Authentication tokens in secure headers
- Audio files encrypted during upload

**At rest:**
- Passwords hashed in Supabase
- Audio files NOT stored (processed and discarded)
- Usage logs stored securely

**Authentication:**
- JWT tokens expire automatically
- Tokens validated on every request
- Users can sign out to invalidate tokens

### What Each Component Can Access

**User's app:**
- Can only access their own data
- Must provide valid token for all requests
- Cannot see other users' information

**Your backend:**
- Has admin access to database
- Can make OpenAI API calls
- Validates all user requests

**Supabase:**
- Handles user authentication
- Stores user accounts securely
- Provides row-level security

## Cost and Scaling

### How Costs Work

**OpenAI API costs (you pay):**
- Whisper: ~$0.006 per minute of audio
- GPT-4: ~$0.03 per 1K tokens
- All users' usage combined

**Hosting costs:**
- Backend server: $5-20/month initially
- Supabase: Free up to 50K users
- Scales up as you grow

### How It Scales

**Few users (0-100):**
- Single backend server handles everything
- Supabase free tier sufficient
- Low OpenAI costs

**More users (100-1000):**
- Same backend can handle load
- May need paid Supabase plan
- Monitor OpenAI costs

**Many users (1000+):**
- May need multiple backend servers
- Database optimization needed
- Consider usage limits per user

## Deployment Requirements

### What You Need to Set Up

**1. Backend hosting:**
- Node.js hosting (Railway, Heroku, VPS)
- Environment variables configured
- Domain name (optional but recommended)

**2. Database:**
- Supabase project created
- Database schema installed
- API keys configured

**3. OpenAI account:**
- API key with billing set up
- Usage monitoring configured

**4. Client app distribution:**
- Apps built with correct backend URL
- Code signing for security (optional)
- Update mechanism (optional)

### Environment Variables Needed

```env
# Your backend needs these:
OPENAI_API_KEY=sk-your-key-here
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-key
JWT_SECRET=your-secure-secret
PORT=3001
NODE_ENV=production
```

## Monitoring and Maintenance

### What to Monitor

**Backend health:**
- Server uptime and response times
- Error rates and logs
- Memory and CPU usage

**Usage patterns:**
- Requests per user
- OpenAI API costs
- Popular features

**User accounts:**
- New signups
- Active users
- Authentication errors

### Regular Maintenance

**Weekly:**
- Check error logs
- Monitor OpenAI costs
- Review usage patterns

**Monthly:**
- Update dependencies
- Review security
- Analyze user feedback

**As needed:**
- Scale server resources
- Update OpenAI models
- Add new features

## Troubleshooting Common Issues

### "App can't connect to backend"
- Check backend URL in app config
- Verify backend server is running
- Check DNS/domain configuration
- Test with curl or browser

### "Authentication not working"
- Verify Supabase configuration
- Check JWT secret is set
- Look at backend logs for errors
- Test auth endpoints directly

### "Voice transcription failing"
- Check OpenAI API key is valid
- Verify OpenAI account has credits
- Check audio file format/size
- Review backend logs

This architecture gives you complete control over the user experience while handling all the complexity of AI processing, authentication, and usage tracking behind the scenes.
