# Usage Statistics Interface

## Overview

This document describes the dedicated usage statistics interface that replaces the previous implementation where usage data was incorrectly displayed in the transcription/AI response window.

## Changes Made

### 1. New Dedicated Usage Modal

- **Location**: A modal dialog overlay that appears when users click "View Usage"
- **Purpose**: Provides a clean, organized view of usage statistics separate from the main transcription interface
- **Accessibility**: Includes proper ARIA labels, keyboard navigation (ESC to close), and focus management

### 2. Improved Data Presentation

The new interface displays:

- **Main Statistics Grid**: 
  - Total Requests
  - Total Tokens Used
  - Audio Duration (seconds)

- **Service Breakdown**: 
  - Voice Transcription requests
  - AI Processing requests
  - Sorted by usage frequency

### 3. User Experience Improvements

- **Loading States**: Shows loading indicators while fetching data
- **Error Handling**: Graceful error handling with user-friendly messages
- **Responsive Design**: Works well on different screen sizes

## Technical Implementation

### Files Modified

1. **assets/index.html**
   - Added modal HTML structure
   - Added CSS styles for the modal and its components

2. **assets/renderer.js**
   - Replaced `showUsageStats()` method to show modal instead of polluting transcription area
   - Added modal management methods (`showUsageModal`, `hideUsageModal`, `setUsageModalLoading`)
   - Added data population methods (`populateUsageModal`, `populateUsageBreakdown`)
   - Added keyboard and click event handlers for modal interaction

### Key Features

- **Modal Overlay**: Dark overlay with centered modal content
- **Close Mechanisms**: X button, ESC key, or clicking outside modal
- **Data Formatting**: Numbers formatted with locale-appropriate separators
- **Service Mapping**: Friendly names for technical endpoint names

## Usage

1. User clicks "View Usage" button in the settings panel
2. Modal opens with loading state
3. Usage data is fetched from the backend
4. Modal is populated with formatted statistics
5. User can close modal via multiple methods

## Benefits

- **Separation of Concerns**: Usage data no longer pollutes the transcription interface
- **Better UX**: Dedicated, well-designed interface for viewing statistics
- **Accessibility**: Proper modal implementation with keyboard navigation
- **Clarity**: Clear presentation of usage metrics
- **Maintainability**: Modular code structure for easy future enhancements

## Future Enhancements

Potential improvements could include:
- Date range selection (7 days, 30 days, 90 days, custom)
- Usage charts and graphs
- Export functionality
- Usage alerts and notifications 