# Vibe Typer Deployment Guide

This guide covers deploying the Vibe Typer backend service and distributing the client application.

## Backend Deployment

### Option 1: Railway (Recommended)

Railway provides easy deployment for Node.js applications with automatic HTTPS and environment management.

1. **Prepare for deployment:**
   ```bash
   cd backend
   # Ensure package.json has correct start script
   npm run build
   ```

2. **Deploy to Railway:**
   - Install Railway CLI: `npm install -g @railway/cli`
   - Login: `railway login`
   - Initialize: `railway init`
   - Deploy: `railway up`

3. **Set environment variables in Railway dashboard:**
   ```
   NODE_ENV=production
   PORT=3001
   OPENAI_API_KEY=sk-your-openai-api-key
   SUPABASE_URL=https://your-project.supabase.co
   SUPABASE_ANON_KEY=your-supabase-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
   JWT_SECRET=your-secure-jwt-secret
   ```

### Option 2: Heroku

1. **Prepare Procfile:**
   ```bash
   echo "web: npm start" > backend/Procfile
   ```

2. **Deploy:**
   ```bash
   cd backend
   git init
   git add .
   git commit -m "Initial commit"
   heroku create your-app-name
   git push heroku main
   ```

3. **Set environment variables:**
   ```bash
   heroku config:set NODE_ENV=production
   heroku config:set OPENAI_API_KEY=sk-your-key
   heroku config:set SUPABASE_URL=https://your-project.supabase.co
   # ... other variables
   ```

### Option 3: VPS/Self-hosted

1. **Server setup:**
   ```bash
   # Install Node.js 18+
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   
   # Install PM2 for process management
   npm install -g pm2
   ```

2. **Deploy application:**
   ```bash
   # Upload backend files to server
   scp -r backend/ user@your-server:/path/to/app/
   
   # On server
   cd /path/to/app/backend
   npm install --production
   npm run build
   
   # Create ecosystem file for PM2
   cat > ecosystem.config.js << EOF
   module.exports = {
     apps: [{
       name: 'vibe-typer-backend',
       script: 'dist/server.js',
       instances: 'max',
       exec_mode: 'cluster',
       env: {
         NODE_ENV: 'production',
         PORT: 3001
       }
     }]
   };
   EOF
   
   # Start with PM2
   pm2 start ecosystem.config.js
   pm2 save
   pm2 startup
   ```

3. **Set up reverse proxy (Nginx):**
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       location / {
           proxy_pass http://localhost:3001;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

## Client Application Distribution

### Building for Distribution

1. **Update configuration:**
   ```typescript
   // Update src/config/manager.ts with production backend URL
   private defaultConfig: AppConfig = {
     authConfig: {
       supabaseUrl: 'https://your-project.supabase.co',
       supabaseAnonKey: 'your-supabase-anon-key',
       backendUrl: 'https://your-backend-domain.com', // Update this
     },
     // ... other config
   };
   ```

2. **Build the application:**
   ```bash
   npm run build
   npm run dist
   ```

### Distribution Options

#### Option 1: GitHub Releases

1. **Create release builds:**
   ```bash
   npm run dist
   ```

2. **Upload to GitHub:**
   - Create a new release on GitHub
   - Upload the generated files from `dist/` directory
   - Include installation instructions

#### Option 2: Auto-updater Setup

1. **Configure electron-updater:**
   ```json
   // In package.json
   "build": {
     "publish": {
       "provider": "github",
       "owner": "your-username",
       "repo": "vibe-typer"
     }
   }
   ```

2. **Enable auto-updates in main.ts:**
   ```typescript
   import { autoUpdater } from 'electron-updater';
   
   app.whenReady().then(() => {
     if (process.env.NODE_ENV === 'production') {
       autoUpdater.checkForUpdatesAndNotify();
     }
   });
   ```

## Environment Configuration

### Production Environment Variables

**Backend (.env):**
```env
NODE_ENV=production
PORT=3001
OPENAI_API_KEY=sk-your-production-key
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-production-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-production-service-role-key
JWT_SECRET=your-very-secure-jwt-secret-at-least-32-chars
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
LOG_LEVEL=info
```

**Client Configuration:**
- Update backend URL in `src/config/manager.ts`
- Ensure Supabase URL and anon key match backend

## Security Considerations

### Backend Security

1. **Environment Variables:**
   - Never commit `.env` files
   - Use strong JWT secrets (32+ characters)
   - Rotate keys regularly

2. **CORS Configuration:**
   ```typescript
   // Update CORS origins for production
   cors: {
     origin: ['https://your-domain.com'],
     credentials: true,
   }
   ```

3. **Rate Limiting:**
   - Configure appropriate rate limits
   - Monitor for abuse patterns

### Client Security

1. **Code Signing:**
   ```json
   // In package.json build config
   "build": {
     "win": {
       "certificateFile": "path/to/certificate.p12",
       "certificatePassword": "password"
     },
     "mac": {
       "identity": "Developer ID Application: Your Name"
     }
   }
   ```

## Monitoring and Maintenance

### Backend Monitoring

1. **Health Checks:**
   - Monitor `/api/health/detailed` endpoint
   - Set up uptime monitoring (UptimeRobot, Pingdom)

2. **Logging:**
   - Monitor application logs
   - Set up log aggregation (ELK stack, Datadog)

3. **Database Monitoring:**
   - Monitor Supabase dashboard
   - Set up alerts for usage limits

### Client Updates

1. **Version Management:**
   - Use semantic versioning
   - Test updates thoroughly
   - Provide rollback mechanism

2. **User Communication:**
   - Notify users of important updates
   - Provide migration guides for breaking changes

## Troubleshooting

### Common Issues

1. **CORS Errors:**
   - Check backend CORS configuration
   - Verify client is using correct backend URL

2. **Authentication Failures:**
   - Verify Supabase configuration
   - Check JWT token expiration

3. **API Rate Limits:**
   - Monitor OpenAI usage
   - Implement user-specific rate limiting

### Support

- Monitor user feedback
- Provide clear error messages
- Maintain documentation
- Set up support channels

## Cost Management

### OpenAI API Costs

1. **Monitor Usage:**
   - Track usage via backend logs
   - Set up OpenAI usage alerts
   - Implement usage caps per user

2. **Optimization:**
   - Use cost-effective models (gpt-4o-mini)
   - Implement caching where appropriate
   - Monitor token usage patterns

### Infrastructure Costs

1. **Backend Hosting:**
   - Start with basic plans
   - Scale based on usage
   - Monitor resource utilization

2. **Database:**
   - Supabase free tier supports up to 50,000 monthly active users
   - Monitor database size and requests

This deployment guide should help you successfully deploy Vibe Typer to production. Remember to test thoroughly in a staging environment before deploying to production.
