# Run Vibe Typer Locally for Testing

Quick and easy setup to test the new backend system on your local computer.

## Why Test Locally First?

- **No server setup needed** - runs on your computer
- **Instant feedback** - see changes immediately
- **Easy debugging** - all logs visible in terminal
- **Free testing** - no hosting costs
- **Quick iterations** - modify and test rapidly

## Prerequisites

### What You Need
- Node.js 18+ installed on your computer
- OpenAI API key
- Supabase account (free tier is fine)
- About 10 minutes to set up

### Check if Node.js is Installed
```bash
node --version  # Should show v18.x.x or higher
npm --version   # Should show 9.x.x or higher
```

If not installed, download from [nodejs.org](https://nodejs.org/)

## Step 1: Set Up Supabase (One-time setup)

### Create Supabase Project
1. Go to [supabase.com](https://supabase.com)
2. Sign up/sign in
3. Click "New Project"
4. Choose organization and enter:
   - **Name**: `vibe-typer-test`
   - **Database Password**: (choose a strong password)
   - **Region**: (closest to you)
5. Click "Create new project"
6. Wait 2-3 minutes for setup to complete

### Get Your Supabase Credentials
1. In your Supabase dashboard, go to **Settings** → **API**
2. Copy these values (you'll need them later):
   - **Project URL** (looks like: `https://abcdefgh.supabase.co`)
   - **anon public key** (starts with `eyJ...`)
   - **service_role secret key** (starts with `eyJ...`)

### Set Up Database Schema
1. In Supabase dashboard, go to **SQL Editor**
2. Click "New query"
3. Copy the entire contents of `backend/supabase-schema.sql`
4. Paste into the SQL editor
5. Click "Run" (bottom right)
6. You should see "Success. No rows returned"

## Step 2: Set Up Backend Locally

### Install Backend Dependencies
```bash
# Navigate to backend directory
cd backend

# Install dependencies
npm install

# Create environment file
cp .env.example .env
```

### Configure Environment Variables
Edit `backend/.env` with your credentials:

```env
# Server Configuration
PORT=3001
NODE_ENV=development

# OpenAI Configuration
OPENAI_API_KEY=sk-your-openai-api-key-here

# Supabase Configuration (from Step 1)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# JWT Configuration
JWT_SECRET=local-development-secret-key-for-testing

# Rate Limiting (relaxed for testing)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Logging
LOG_LEVEL=debug
```

**Important**: Replace the placeholder values with your actual:
- OpenAI API key
- Supabase URL and keys (from Step 1)

### Build and Start Backend
```bash
# Build TypeScript code
npm run build

# Start in development mode (with auto-reload)
npm run dev
```

You should see:
```
Vibe Typer Backend server running on port 3001
Environment: development
Configuration validated successfully
```

### Test Backend is Working
Open a new terminal and test:
```bash
# Test health endpoint
curl http://localhost:3001/api/health

# Should return:
# {"success":true,"message":"Vibe Typer Backend is healthy",...}
```

## Step 3: Set Up Client Locally

### Install Client Dependencies
```bash
# In a new terminal, go back to main directory
cd ..

# Install client dependencies
npm install
```

### Update Client Configuration
The client should already be configured for local development, but verify in `src/config/manager.ts`:

```typescript
private defaultConfig: AppConfig = {
  authConfig: {
    supabaseUrl: 'https://your-project.supabase.co',  // Your Supabase URL
    supabaseAnonKey: 'your-supabase-anon-key',        // Your anon key
    backendUrl: 'http://localhost:3001',              // Local backend
  },
  // ... rest of config
};
```

### Build and Start Client
```bash
# Build TypeScript code
npm run build

# Start the Electron app
npm run dev
```

The Vibe Typer app should open!

## Step 4: Test the Complete System

### Test Authentication
1. **Open the app** (should show Vibe Typer window)
2. **Click "Settings"** (gear icon)
3. **Create account**:
   - Click "Create Account"
   - Enter email and password
   - Click "Create Account"
   - Should see "Account created successfully!"
4. **Sign in**:
   - Enter same email/password
   - Click "Sign In"
   - Should see "Signed in successfully!"

### Test Voice Recognition
1. **Make sure you're signed in** (should see your email in settings)
2. **Click in any text field** (like a text editor, browser, etc.)
3. **Press the hotkey** (`Ctrl+Shift+Space` or `Cmd+Shift+Space`)
4. **Speak clearly**: "Hello, this is a test"
5. **Release the hotkey**
6. **Text should appear** where your cursor was!

### Test AI Features
1. **Click in a text field**
2. **Press hotkey and say**: "Write an email about meeting tomorrow"
3. **Release hotkey**
4. **Should get AI-generated email text**

## Monitoring and Debugging

### Watch Backend Logs
In the backend terminal, you'll see real-time logs:
```
info: POST /api/auth/signin {"userId":"...","email":"<EMAIL>"}
info: Transcription request received {"userId":"...","fileSize":45678}
info: Transcription completed {"userId":"...","transcriptionLength":25}
```

### Watch Client Logs
In the client terminal, you'll see Electron logs:
```
User authenticated: <EMAIL>
Transcription request sent to backend
Transcription received: "Hello, this is a test"
```

### Common Issues and Solutions

**"Backend connection failed"**
- Check backend is running: `curl http://localhost:3001/api/health`
- Check .env file has correct values
- Look at backend terminal for errors

**"Authentication failed"**
- Check Supabase credentials in .env
- Verify database schema was installed
- Check backend logs for auth errors

**"Voice recognition not working"**
- Check OpenAI API key in .env
- Verify microphone permissions
- Check backend logs for transcription errors

**"No text appears when speaking"**
- Make sure you're signed in
- Check cursor is in a text field
- Try speaking more clearly/slowly

## Development Workflow

### Making Changes

**Backend changes:**
1. Edit files in `backend/src/`
2. Save (auto-reloads with `npm run dev`)
3. Test immediately

**Client changes:**
1. Edit files in `src/`
2. Run `npm run build`
3. Restart client app

### Testing Different Scenarios

**Test with multiple users:**
1. Create multiple accounts
2. Sign out and sign in with different accounts
3. Verify usage tracking works

**Test error handling:**
1. Stop backend while client is running
2. Try invalid credentials
3. Test with no internet connection

## Next Steps

Once everything works locally:

1. **Deploy to VPS** using `VPS-DEPLOYMENT.md`
2. **Update client config** to point to your server
3. **Distribute to users**

## Quick Reference

### Start Everything
```bash
# Terminal 1: Backend
cd backend && npm run dev

# Terminal 2: Client
npm run dev
```

### Stop Everything
- `Ctrl+C` in both terminals
- Close the Electron app

### Reset Database
If you need to start fresh:
1. Go to Supabase dashboard
2. Settings → Database → Reset database
3. Re-run the SQL schema

### Check Status
```bash
# Test backend
curl http://localhost:3001/api/health

# Check if client can reach backend
curl http://localhost:3001/api/openai/test
```

This local setup is perfect for development and testing. You can iterate quickly, see all logs, and debug issues easily before deploying to production!
