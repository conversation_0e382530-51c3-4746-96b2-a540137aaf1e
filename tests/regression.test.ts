import { pcm16leToWav } from '../src/audio/pcmToWav';
import { SpeechRecognizer } from '../src/speech/recognizer';
import axios from 'axios';
import FormData from 'form-data';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('PCM to WAV Regression Test', () => {
  let speechRecognizer: SpeechRecognizer;

  beforeEach(() => {
    speechRecognizer = new SpeechRecognizer();
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  test('should convert 100ms silence PCM to WAV and send to mocked endpoint', async () => {
    // Step 1: Mock a 100ms silence PCM buffer
    const sampleRate = 16000; // 16kHz
    const channels = 1; // Mono
    const durationMs = 100; // 100ms
    const samplesPerMs = sampleRate / 1000; // 16 samples per ms
    const totalSamples = samplesPerMs * durationMs; // 1600 samples
    const bytesPerSample = 2; // 16-bit = 2 bytes
    const pcmLen = totalSamples * bytesPerSample; // 3200 bytes

    // Create PCM buffer filled with zeros (silence)
    const pcmBuffer = Buffer.alloc(pcmLen);
    
    // Step 2: Convert PCM to WAV
    const wavBuffer = pcm16leToWav(pcmBuffer, sampleRate, channels);
    
    // Step 3: Verify WAV header is present and length is correct
    expect(wavBuffer.length).toBe(pcmLen + 44); // PCM data + 44 byte header
    
    // Verify RIFF header
    expect(wavBuffer.toString('ascii', 0, 4)).toBe('RIFF');
    
    // Verify WAVE format
    expect(wavBuffer.toString('ascii', 8, 12)).toBe('WAVE');
    
    // Verify fmt chunk
    expect(wavBuffer.toString('ascii', 12, 16)).toBe('fmt ');
    
    // Verify data chunk
    expect(wavBuffer.toString('ascii', 36, 40)).toBe('data');
    
    // Verify data size in header matches PCM length
    const dataSize = wavBuffer.readUInt32LE(40);
    expect(dataSize).toBe(pcmLen);
    
    // Step 4: Mock Axios POST response
    const mockResponse = {
      data: 'Mock transcription of 100ms silence audio',
      status: 200,
      headers: { 'content-type': 'application/json' }
    };
    
    mockedAxios.post.mockResolvedValue(mockResponse);
    
    // Step 5: Set up API key for the test
    speechRecognizer.setApiKey('test-api-key');
    
    // Step 6: Send WAV to mocked endpoint
    const transcription = await speechRecognizer.transcribe(wavBuffer);
    
    // Step 7: Verify the axios call was made with correct parameters
    expect(mockedAxios.post).toHaveBeenCalledTimes(1);
    
    const [url, formData, config] = mockedAxios.post.mock.calls[0];
    
    // Verify URL
    expect(url).toBe('https://api.openai.com/v1/audio/transcriptions');
    
    // Verify FormData was used
    expect(formData).toBeInstanceOf(FormData);
    
    // Verify headers contain authorization
    expect(config?.headers).toHaveProperty('Authorization', 'Bearer test-api-key');
    
    // Verify timeout is set
    expect(config?.timeout).toBe(30000);
    
    // Step 8: Assert transcription result
    expect(transcription).toBe('Mock transcription of 100ms silence audio');
    
    // Step 9: Additional assertions about the WAV buffer format
    // Verify PCM format (1 = PCM)
    expect(wavBuffer.readUInt16LE(20)).toBe(1);
    
    // Verify channels
    expect(wavBuffer.readUInt16LE(22)).toBe(channels);
    
    // Verify sample rate
    expect(wavBuffer.readUInt32LE(24)).toBe(sampleRate);
    
    // Verify bits per sample
    expect(wavBuffer.readUInt16LE(34)).toBe(16);
    
    // Verify file size in header
    const fileSize = wavBuffer.readUInt32LE(4);
    expect(fileSize).toBe(wavBuffer.length - 8); // Total size minus first 8 bytes
  });

  test('should handle 100ms silence with different audio parameters', async () => {
    // Test with different sample rates and channel configurations
    const testCases = [
      { sampleRate: 8000, channels: 1, durationMs: 100 },   // 8kHz mono
      { sampleRate: 44100, channels: 2, durationMs: 100 },  // 44.1kHz stereo
      { sampleRate: 48000, channels: 1, durationMs: 100 }   // 48kHz mono
    ];

    for (const testCase of testCases) {
      const { sampleRate, channels, durationMs } = testCase;
      
      // Calculate PCM buffer size
      const samplesPerMs = sampleRate / 1000;
      const totalSamples = samplesPerMs * durationMs;
      const pcmLen = totalSamples * channels * 2; // 2 bytes per sample
      
      // Create silence PCM buffer
      const pcmBuffer = Buffer.alloc(pcmLen);
      
      // Convert to WAV
      const wavBuffer = pcm16leToWav(pcmBuffer, sampleRate, channels);
      
      // Assert header present and correct length
      expect(wavBuffer.length).toBe(pcmLen + 44);
      expect(wavBuffer.toString('ascii', 0, 4)).toBe('RIFF');
      expect(wavBuffer.toString('ascii', 8, 12)).toBe('WAVE');
      
      // Verify audio parameters in header
      expect(wavBuffer.readUInt16LE(22)).toBe(channels);
      expect(wavBuffer.readUInt32LE(24)).toBe(sampleRate);
      expect(wavBuffer.readUInt32LE(40)).toBe(pcmLen);
    }
  });

  test('should fail gracefully with invalid WAV data', async () => {
    // Create an invalid buffer (too small)
    const invalidBuffer = Buffer.alloc(20);
    
    // Mock axios to avoid actual API call
    mockedAxios.post.mockResolvedValue({ data: 'should not reach here' });
    
    // Set API key
    speechRecognizer.setApiKey('test-api-key');
    
    // Should throw error due to invalid WAV format
    await expect(speechRecognizer.transcribe(invalidBuffer)).rejects.toThrow(
      'Invalid audio format: Expected WAV format with proper header'
    );
    
    // Verify axios was not called
    expect(mockedAxios.post).not.toHaveBeenCalled();
  });

  test('should work in demo mode without API key', async () => {
    // Create 100ms silence PCM buffer
    const sampleRate = 16000;
    const channels = 1;
    const durationMs = 100;
    const samplesPerMs = sampleRate / 1000;
    const totalSamples = samplesPerMs * durationMs;
    const pcmLen = totalSamples * 2;
    
    const pcmBuffer = Buffer.alloc(pcmLen);
    const wavBuffer = pcm16leToWav(pcmBuffer, sampleRate, channels);
    
    // Don't set API key (demo mode)
    const transcription = await speechRecognizer.transcribe(wavBuffer);
    
    // Should return mock transcription
    expect(transcription).toContain('Mock transcription of');
    expect(transcription).toContain(`${wavBuffer.length} bytes`);
    
    // Verify axios was not called in demo mode
    expect(mockedAxios.post).not.toHaveBeenCalled();
  });
});
