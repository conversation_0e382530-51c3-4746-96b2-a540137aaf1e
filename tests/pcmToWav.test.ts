import { pcm16leToWav } from '../src/audio/pcmToWav';

describe('PCM to WAV Conversion', () => {
  test('should convert PCM to WAV with correct header', () => {
    const sampleRate = 16000;
    const channels = 1;
    const silenceBuffer = Buffer.alloc(sampleRate * channels * 2); // 1 second of silence
    const wavBuffer = pcm16leToWav(silenceBuffer, sampleRate, channels);

    // Verify the RIFF and WAVE headers
    const riffHeader = wavBuffer.toString('utf8', 0, 4);
    const waveHeader = wavBuffer.toString('utf8', 8, 12);

    expect(riffHeader).toBe('RIFF');
    expect(waveHeader).toBe('WAVE');

    // Verify file size (should be 44 + data size)
    const expectedFileSize = 44 + silenceBuffer.length;
    const actualFileSize = wavBuffer.readUInt32LE(4) + 8;
    expect(actualFileSize).toBe(expectedFileSize);

    // Verify fmt chunk
    const fmtHeader = wavBuffer.toString('utf8', 12, 16);
    expect(fmtHeader).toBe('fmt ');

    // Verify PCM format
    const audioFormat = wavBuffer.readUInt16LE(20);
    expect(audioFormat).toBe(1);

    // Verify channels
    const numChannels = wavBuffer.readUInt16LE(22);
    expect(numChannels).toBe(channels);

    // Verify sample rate
    const actualSampleRate = wavBuffer.readUInt32LE(24);
    expect(actualSampleRate).toBe(sampleRate);

    // Verify byte rate
    const expectedByteRate = sampleRate * channels * 2;
    const actualByteRate = wavBuffer.readUInt32LE(28);
    expect(actualByteRate).toBe(expectedByteRate);

    // Verify block align
    const expectedBlockAlign = channels * 2;
    const actualBlockAlign = wavBuffer.readUInt16LE(32);
    expect(actualBlockAlign).toBe(expectedBlockAlign);

    // Verify bits per sample
    const bitsPerSample = wavBuffer.readUInt16LE(34);
    expect(bitsPerSample).toBe(16);

    // Verify data chunk
    const dataHeader = wavBuffer.toString('utf8', 36, 40);
    expect(dataHeader).toBe('data');

    // Verify data size
    const dataSize = wavBuffer.readUInt32LE(40);
    expect(dataSize).toBe(silenceBuffer.length);
  });

  test('should handle different parameters correctly', () => {
    // Test with stereo and different sample rate
    const sampleRate = 44100;
    const channels = 2;
    const testBuffer = Buffer.alloc(1000); // Small test buffer
    const wavBuffer = pcm16leToWav(testBuffer, sampleRate, channels);

    // Verify the basic headers
    const riffHeader = wavBuffer.toString('utf8', 0, 4);
    const waveHeader = wavBuffer.toString('utf8', 8, 12);

    expect(riffHeader).toBe('RIFF');
    expect(waveHeader).toBe('WAVE');

    // Verify sample rate and channels
    const actualSampleRate = wavBuffer.readUInt32LE(24);
    const numChannels = wavBuffer.readUInt16LE(22);
    
    expect(actualSampleRate).toBe(sampleRate);
    expect(numChannels).toBe(channels);
  });
});
