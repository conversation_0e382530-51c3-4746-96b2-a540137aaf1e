import React, { useState, useEffect } from 'react';
import { 
  Mic, 
  Zap, 
  Shield, 
  Globe, 
  Code, 
  PenTool, 
  GraduationCap, 
  Briefcase, 
  Heart,
  Users,
  CheckCircle,
  ArrowRight,
  Download,
  Menu,
  X,
  ChevronDown,
  Sparkles,
  Clock,
  Target,
  Layers,
  Settings,
  Headphones
} from 'lucide-react';

function App() {
  const [isVisible, setIsVisible] = useState(false);
  const [activeFeature, setActiveFeature] = useState(0);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  
  useEffect(() => {
    setIsVisible(true);
  }, []);

  const features = [
    {
      icon: <Zap className="w-6 h-6" />,
      title: "Lightning Fast Transcription",
      description: "Convert speech to text at 220+ words per minute - 5x faster than traditional typing",
      detail: "Experience real-time transcription that keeps up with your thoughts and natural speaking pace."
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Complete Privacy Protection",
      description: "Secure voice processing with enterprise-grade privacy protection",
      detail: "Your voice data is protected with advanced security measures, ensuring absolute privacy and security."
    },
    {
      icon: <Globe className="w-6 h-6" />,
      title: "Universal Application Support",
      description: "Works seamlessly in any application across Windows, macOS, and Linux",
      detail: "From code editors to email clients, Vibe Typer integrates with your entire workflow."
    }
  ];

  const userTypes = [
    {
      icon: <Code className="w-8 h-8" />,
      title: "Software Developers",
      description: "Accelerate coding and documentation with voice-driven development workflows",
      benefits: ["Faster code documentation", "Voice-to-code capabilities", "Reduced typing strain"]
    },
    {
      icon: <PenTool className="w-8 h-8" />,
      title: "Content Creators", 
      description: "Break through creative blocks and capture ideas at the speed of thought",
      benefits: ["Natural idea capture", "Overcome writer's block", "Enhanced creativity flow"]
    },
    {
      icon: <GraduationCap className="w-8 h-8" />,
      title: "Students & Researchers",
      description: "Transform note-taking and academic writing with intelligent voice input",
      benefits: ["Efficient note-taking", "Academic writing", "Research documentation"]
    },
    {
      icon: <Briefcase className="w-8 h-8" />,
      title: "Business Professionals",
      description: "Streamline communication and documentation for maximum productivity",
      benefits: ["Professional emails", "Meeting notes", "Report generation"]
    },
    {
      icon: <Heart className="w-8 h-8" />,
      title: "Accessibility Champions",
      description: "Empowering users with typing difficulties through inclusive voice technology",
      benefits: ["Inclusive design", "Reduced physical strain", "Equal digital access"]
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "Productivity Enthusiasts",
      description: "Anyone seeking to maximize efficiency and modernize their workflow",
      benefits: ["Time optimization", "Workflow enhancement", "Future-ready tools"]
    }
  ];

  const keyBenefits = [
    {
      icon: <Sparkles className="w-8 h-8" />,
      title: "AI Voice Commands",
      description: "Powerful voice commands to write content, answer questions, rewrite text, and compose replies using GPT-4",
      highlight: "Voice-to-AI Integration"
    },
    {
      icon: <Clock className="w-8 h-8" />,
      title: "Instant Text Insertion",
      description: "Text appears exactly where you need it without copy-pasting or switching applications",
      highlight: "Zero Friction Workflow"
    },
    {
      icon: <Target className="w-8 h-8" />,
      title: "Precision Accuracy",
      description: "Advanced speech recognition with 99%+ accuracy across multiple languages and accents",
      highlight: "Multi-Language Support"
    },
    {
      icon: <Layers className="w-8 h-8" />,
      title: "Seamless Integration",
      description: "Works with every application on your system - no setup or configuration required",
      highlight: "Universal Compatibility"
    },
    {
      icon: <Settings className="w-8 h-8" />,
      title: "Customizable Commands",
      description: "Create voice shortcuts for formatting, punctuation, and application-specific actions",
      highlight: "Voice Shortcuts"
    },
    {
      icon: <Headphones className="w-8 h-8" />,
      title: "Noise Cancellation",
      description: "Advanced audio processing filters background noise for crystal-clear transcription",
      highlight: "Crystal Clear Audio"
    }
  ];

  return (
    <div className="min-h-screen bg-stone-100">
      {/* Navigation */}
      <nav className="bg-stone-100/95 backdrop-blur-sm border-b border-stone-300/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-200 rounded-lg">
                <Mic className="w-6 h-6 text-purple-700" />
              </div>
              <span className="text-2xl font-bold text-stone-900">Vibe Typer</span>
            </div>
            
            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <div className="flex items-center gap-1 text-stone-700 hover:text-stone-900 cursor-pointer transition-colors">
                <span>Product</span>
                <ChevronDown className="w-4 h-4" />
              </div>
              <a href="#" className="text-stone-700 hover:text-stone-900 transition-colors">Features</a>
              <a href="#" className="text-stone-700 hover:text-stone-900 transition-colors">Pricing</a>
              <div className="flex items-center gap-1 text-stone-700 hover:text-stone-900 cursor-pointer transition-colors">
                <span>Support</span>
                <ChevronDown className="w-4 h-4" />
              </div>
              <button className="bg-purple-200 text-purple-800 px-6 py-2 rounded-lg font-medium hover:bg-purple-300 transition-all duration-300">
                Download for free
              </button>
            </div>

            {/* Mobile menu button */}
            <button 
              className="md:hidden p-2"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>

          {/* Mobile Navigation */}
          {mobileMenuOpen && (
            <div className="md:hidden py-4 border-t border-stone-300">
              <div className="flex flex-col space-y-4">
                <a href="#" className="text-stone-700 hover:text-stone-900 transition-colors">Product</a>
                <a href="#" className="text-stone-700 hover:text-stone-900 transition-colors">Features</a>
                <a href="#" className="text-stone-700 hover:text-stone-900 transition-colors">Pricing</a>
                <a href="#" className="text-stone-700 hover:text-stone-900 transition-colors">Support</a>
                <button className="bg-purple-200 text-purple-800 px-6 py-2 rounded-lg font-medium hover:bg-purple-300 transition-all duration-300 w-fit">
                  Download for free
                </button>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative overflow-hidden bg-stone-100 py-24 lg:py-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className={`text-center transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <h1 className="text-5xl lg:text-7xl font-light text-stone-600 mb-4 leading-tight">
              Don't type,{' '}
              <span className="font-bold text-stone-900">just speak</span>
            </h1>
            
            <div className="max-w-4xl mx-auto mb-12">
              <p className="text-xl lg:text-2xl text-stone-700 mb-4 leading-relaxed">
                Revolutionary voice dictation that transforms speech into perfect text:
              </p>
              <p className="text-xl lg:text-2xl text-stone-900 font-medium leading-relaxed">
                5x faster than typing, AI-enhanced accuracy, works everywhere.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
              <button className="group bg-stone-200 border-2 border-stone-400 text-stone-900 px-8 py-4 rounded-lg font-medium text-lg hover:bg-stone-300 hover:border-stone-500 transition-all duration-300 transform hover:scale-105">
                <div className="flex items-center gap-2 justify-center">
                  <Mic className="w-5 h-5" />
                  Try Vibe Typer
                </div>
              </button>
              <button className="group bg-purple-200 text-purple-800 px-8 py-4 rounded-lg font-medium text-lg hover:bg-purple-300 transition-all duration-300 transform hover:scale-105">
                <div className="flex items-center gap-2 justify-center">
                  Download
                </div>
              </button>
            </div>

            <p className="text-stone-600 text-lg">
              Available on Mac, Windows and Linux
            </p>
          </div>
        </div>
      </section>

      {/* Core Features Section */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-light text-stone-700 mb-6">
              Why Vibe Typer <span className="font-bold text-stone-900">Revolutionizes Productivity</span>
            </h2>
            <p className="text-xl text-stone-600 max-w-3xl mx-auto">
              Experience next-generation voice technology that respects your privacy while delivering unmatched speed and accuracy.
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div 
                key={index}
                className="group p-8 bg-stone-100 rounded-3xl hover:bg-stone-200 transition-all duration-300 transform hover:scale-105 cursor-pointer border border-stone-300/50"
                onMouseEnter={() => setActiveFeature(index)}
              >
                <div className="text-purple-700 mb-6 group-hover:scale-110 transition-transform duration-300">
                  {feature.icon}
                </div>
                <h3 className="text-2xl font-bold text-stone-900 mb-4">{feature.title}</h3>
                <p className="text-stone-700 mb-4 text-lg">{feature.description}</p>
                <p className="text-stone-600">{feature.detail}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-24 bg-stone-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-light text-stone-700 mb-6">
              Simple. <span className="font-bold text-stone-900">Powerful. Instant.</span>
            </h2>
            <p className="text-xl text-stone-600 max-w-3xl mx-auto">
              Three effortless steps to transform your productivity forever.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-12">
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-200 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-purple-800">1</span>
              </div>
              <h3 className="text-2xl font-bold text-stone-900 mb-4">Speak Naturally</h3>
              <p className="text-stone-700 text-lg">
                Simply start speaking in your natural voice. Vibe Typer instantly recognizes your speech patterns and converts them to perfect text.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-200 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-purple-800">2</span>
              </div>
              <h3 className="text-2xl font-bold text-stone-900 mb-4">AI Enhancement</h3>
              <p className="text-stone-700 text-lg">
                Intelligent AI commands let you write content, answer questions, rewrite text, and compose replies - all through natural voice commands.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-200 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-purple-800">3</span>
              </div>
              <h3 className="text-2xl font-bold text-stone-900 mb-4">Instant Integration</h3>
              <p className="text-stone-700 text-lg">
                Text appears exactly where you need it - in any application, any field, any platform. No copying, no pasting, no friction.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Speed Comparison */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-light text-stone-700 mb-6">
              <span className="font-bold text-stone-900">5x Faster</span> Than Traditional Typing
            </h2>
            <p className="text-xl text-stone-600 max-w-3xl mx-auto">
              Break free from keyboard limitations. Your thoughts deserve to flow at the speed of speech.
            </p>
          </div>
          
          <div className="flex flex-col lg:flex-row justify-center items-center gap-16">
            <div className="text-center">
              <div className="text-3xl font-light text-stone-600 mb-4">Traditional Typing</div>
              <div className="text-6xl font-bold text-stone-400 mb-4">45</div>
              <div className="text-xl text-stone-600">words per minute</div>
            </div>
            <div className="text-6xl text-purple-600">
              <ArrowRight className="w-12 h-12" />
            </div>
            <div className="text-center">
              <div className="text-3xl font-light text-stone-700 mb-4">Vibe Typer</div>
              <div className="text-6xl font-bold text-purple-700 mb-4">220+</div>
              <div className="text-xl text-stone-700">words per minute</div>
            </div>
          </div>
        </div>
      </section>

      {/* Key Benefits Section */}
      <section className="py-24 bg-stone-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-light text-stone-700 mb-6">
              Advanced Features <span className="font-bold text-stone-900">That Set Us Apart</span>
            </h2>
            <p className="text-xl text-stone-600 max-w-3xl mx-auto">
              Discover the powerful capabilities that make Vibe Typer the ultimate voice-to-text solution.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {keyBenefits.map((benefit, index) => (
              <div key={index} className="p-8 bg-white rounded-3xl hover:shadow-lg transition-all duration-300 group border border-stone-300/50">
                <div className="text-purple-700 mb-6 group-hover:scale-110 transition-transform duration-300">
                  {benefit.icon}
                </div>
                <div className="mb-3">
                  <span className="inline-block bg-purple-100 text-purple-800 text-sm font-medium px-3 py-1 rounded-full">
                    {benefit.highlight}
                  </span>
                </div>
                <h3 className="text-2xl font-bold text-stone-900 mb-4">{benefit.title}</h3>
                <p className="text-stone-700 text-lg">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Target Users */}
      <section className="py-24 bg-stone-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-light text-stone-300 mb-6">
              Built for <span className="font-bold text-white">Every Professional</span>
            </h2>
            <p className="text-xl text-stone-400 max-w-3xl mx-auto">
              Whether you're creating, coding, studying, or leading, Vibe Typer adapts to your unique workflow and amplifies your productivity.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {userTypes.map((user, index) => (
              <div key={index} className="p-8 bg-stone-800/50 backdrop-blur-sm rounded-3xl hover:bg-stone-800 transition-all duration-300 group border border-stone-700/50">
                <div className="text-purple-400 mb-6 group-hover:scale-110 transition-transform duration-300">
                  {user.icon}
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">{user.title}</h3>
                <p className="text-stone-300 mb-6">{user.description}</p>
                <ul className="space-y-2">
                  {user.benefits.map((benefit, i) => (
                    <li key={i} className="flex items-center text-stone-300">
                      <CheckCircle className="w-5 h-5 text-purple-400 mr-3 flex-shrink-0" />
                      {benefit}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Final CTA */}
      <section className="py-24 bg-stone-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl lg:text-5xl font-light text-stone-300 mb-6">
            Ready to <span className="font-bold text-white">Revolutionize Your Workflow?</span>
          </h2>
          <p className="text-xl text-stone-400 mb-12 max-w-3xl mx-auto">
            Join thousands of professionals who've discovered the power of voice-driven productivity. Experience Vibe Typer free for 14 days.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <button className="group bg-white text-stone-900 px-8 py-4 rounded-lg font-medium text-lg hover:bg-stone-100 transition-all duration-300 transform hover:scale-105">
              <div className="flex items-center gap-2 justify-center">
                <Download className="w-5 h-5" />
                Start Free Trial
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </div>
            </button>
            <button className="border-2 border-stone-600 text-stone-300 px-8 py-4 rounded-lg font-medium text-lg hover:bg-stone-800 hover:border-stone-500 transition-all duration-300">
              Watch Demo
            </button>
          </div>

          <div className="text-sm text-stone-500">
            Free 14-day trial • No credit card required • Works on Windows, macOS, and Linux
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-stone-950 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center gap-3 mb-6 md:mb-0">
              <div className="p-2 bg-purple-900/50 rounded-lg">
                <Mic className="w-6 h-6 text-purple-400" />
              </div>
              <span className="text-2xl font-bold text-white">Vibe Typer</span>
            </div>
            <p className="text-stone-400 text-sm">
              &copy; 2025 Vibe Typer. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;