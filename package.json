{"name": "vibe-typer", "version": "1.0.0", "description": "Cross-platform speech-to-text application with AI-powered features", "main": "dist/main.js", "scripts": {"build": "tsc", "start": "npm run build && electron dist/main.js", "dev": "tsc && electron dist/main.js", "pack": "electron-builder", "dist": "electron-builder --publish=never", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": ["speech-to-text", "whisper", "electron", "cross-platform", "voice-typing"], "author": "Your Name", "license": "MIT", "build": {"appId": "com.yourcompany.vibetyper", "productName": "Vibe Typer", "directories": {"output": "release"}, "files": ["dist/**/*", "assets/**/*"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}, "devDependencies": {"@types/jest": "^30.0.0", "@types/node": "^24.0.13", "electron": "^37.2.1", "electron-builder": "^26.0.12", "jest": "^30.0.4", "ts-jest": "^29.4.0", "typescript": "^5.8.3"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "@types/form-data": "^2.5.2", "axios": "^1.10.0", "form-data": "^4.0.3", "uiohook-napi": "^1.5.4"}}