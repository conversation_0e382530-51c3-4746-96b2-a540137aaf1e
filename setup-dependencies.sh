#!/bin/bash

# Setup script for Vibe Typer dependencies

echo "Setting up dependencies for Vibe Typer..."

# Detect the operating system
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "Detected Linux system"

    # Check if we're on Ubuntu/Debian
    if command -v apt-get &> /dev/null; then
        echo "Installing dependencies with apt-get..."
        echo "You may be prompted for your password."

        # Update package list
        sudo apt-get update

        # Install xdotool for text insertion
        echo "Installing xdotool (text insertion)..."
        sudo apt-get install -y xdotool

        # Install xclip as backup for text insertion
        echo "Installing xclip (clipboard access)..."
        sudo apt-get install -y xclip
        
    # Check if we're on Fedora/RHEL
    elif command -v dnf &> /dev/null; then
        echo "Installing dependencies with dnf..."
        echo "You may be prompted for your password."

        # Install xdotool for text insertion
        echo "Installing xdotool (text insertion)..."
        sudo dnf install -y xdotool

        # Install xclip as backup for text insertion
        echo "Installing xclip (clipboard access)..."
        sudo dnf install -y xclip

    # Check if we're on Arch
    elif command -v pacman &> /dev/null; then
        echo "Installing dependencies with pacman..."
        echo "You may be prompted for your password."

        # Install xdotool for text insertion
        echo "Installing xdotool (text insertion)..."
        sudo pacman -S --noconfirm xdotool

        # Install xclip as backup for text insertion
        echo "Installing xclip (clipboard access)..."
        sudo pacman -S --noconfirm xclip
        
    else
        echo "Unsupported Linux distribution. Please install manually:"
        echo "- xdotool (for text insertion)"
        echo "- xclip (for clipboard access)"
        exit 1
    fi

elif [[ "$OSTYPE" == "darwin"* ]]; then
    echo "Detected macOS system"
    echo "macOS dependencies are handled automatically by the application."
    echo "Note: You may need to grant microphone and accessibility permissions."

elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
    echo "Detected Windows system"
    echo "Windows dependencies are handled automatically by the application."
    
else
    echo "Unsupported operating system: $OSTYPE"
    exit 1
fi

echo ""
echo "Dependency installation completed!"
echo ""
echo "Testing installed dependencies..."

# Note: Audio recording is now handled by Web Audio API (no external dependencies required)
echo "✓ Audio recording: Built-in Web Audio API (no external dependencies required)"

# Test xdotool (Linux only)
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    if command -v xdotool &> /dev/null; then
        echo "✓ xdotool is installed and available"
        xdotool version
    else
        echo "✗ xdotool is not available"
    fi
    
    if command -v xclip &> /dev/null; then
        echo "✓ xclip is installed and available"
        xclip -version
    else
        echo "✗ xclip is not available"
    fi
fi

echo ""
echo "Setup complete! You can now run the application with:"
echo "npm run dev"
echo ""
echo "If you encounter permission issues:"
echo "- All platforms: Grant microphone permissions when prompted by the browser/application"
echo "- Linux: Grant microphone permissions and ensure your user is in the 'audio' group"
echo "- macOS: Grant microphone and accessibility permissions in System Preferences"
echo "- Windows: Grant microphone permissions in Privacy settings"
echo ""
echo "Note: Audio recording now uses the built-in Web Audio API - no external audio"
echo "      software installation required!"
