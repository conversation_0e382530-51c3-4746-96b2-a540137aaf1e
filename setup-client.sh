#!/bin/bash

# Vibe Typer Client Setup Script
# This script sets up the client application for the Vibe Typer

set -e

echo "🚀 Setting up Vibe Typer Client..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Install client dependencies
echo "📦 Installing client dependencies..."
npm install

# Build the TypeScript code
echo "🔨 Building TypeScript code..."
npm run build

echo ""
echo "✅ Client setup complete!"
echo ""
echo "Next steps:"
echo "1. Make sure the backend is running (see setup-backend.sh)"
echo "2. Update the backend URL in the client configuration if needed"
echo "3. Start the client with: npm run dev"
echo ""
echo "For more information, see README.md"
