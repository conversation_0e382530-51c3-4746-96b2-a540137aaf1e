#!/usr/bin/env node

/**
 * Vibe Typer Setup Validation Script
 * This script validates that the transformation was successful
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Vibe Typer Setup...\n');

let errors = [];
let warnings = [];

// Test 1: Check if backend directory exists
console.log('1. Checking backend directory...');
if (fs.existsSync('backend')) {
    console.log('   ✅ Backend directory exists');
} else {
    errors.push('Backend directory not found');
}

// Test 2: Check backend package.json
console.log('2. Checking backend package.json...');
try {
    const backendPackage = JSON.parse(fs.readFileSync('backend/package.json', 'utf8'));
    const requiredDeps = ['@supabase/supabase-js', 'express', 'axios', 'cors', 'helmet'];
    const missingDeps = requiredDeps.filter(dep => !backendPackage.dependencies[dep]);
    
    if (missingDeps.length === 0) {
        console.log('   ✅ All required backend dependencies found');
    } else {
        errors.push(`Missing backend dependencies: ${missingDeps.join(', ')}`);
    }
} catch (error) {
    errors.push('Could not read backend/package.json');
}

// Test 3: Check if .env.example exists
console.log('3. Checking backend configuration...');
if (fs.existsSync('backend/.env.example')) {
    console.log('   ✅ Backend .env.example exists');
} else {
    errors.push('Backend .env.example not found');
}

// Test 4: Check if Supabase schema exists
console.log('4. Checking database schema...');
if (fs.existsSync('backend/supabase-schema.sql')) {
    console.log('   ✅ Supabase schema file exists');
} else {
    errors.push('Supabase schema file not found');
}

// Test 5: Check client package.json for Supabase dependency
console.log('5. Checking client dependencies...');
try {
    const clientPackage = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    if (clientPackage.dependencies['@supabase/supabase-js']) {
        console.log('   ✅ Client has Supabase dependency');
    } else {
        errors.push('Client missing @supabase/supabase-js dependency');
    }
} catch (error) {
    errors.push('Could not read client package.json');
}

// Test 6: Check if auth service exists
console.log('6. Checking authentication service...');
if (fs.existsSync('src/auth/service.ts')) {
    console.log('   ✅ Authentication service exists');
} else {
    errors.push('Authentication service not found');
}

// Test 7: Check if API service exists
console.log('7. Checking API service...');
if (fs.existsSync('src/api/service.ts')) {
    console.log('   ✅ API service exists');
} else {
    errors.push('API service not found');
}

// Test 8: Check if config manager was updated
console.log('8. Checking configuration manager...');
try {
    const configContent = fs.readFileSync('src/config/manager.ts', 'utf8');
    if (configContent.includes('authConfig') && !configContent.includes('openaiApiKey')) {
        console.log('   ✅ Configuration manager updated for authentication');
    } else {
        warnings.push('Configuration manager may not be fully updated');
    }
} catch (error) {
    errors.push('Could not read configuration manager');
}

// Test 9: Check if HTML was updated
console.log('9. Checking UI updates...');
try {
    const htmlContent = fs.readFileSync('assets/index.html', 'utf8');
    if (htmlContent.includes('signInForm') && htmlContent.includes('signUpForm')) {
        console.log('   ✅ HTML updated with authentication forms');
    } else {
        errors.push('HTML not updated with authentication forms');
    }
} catch (error) {
    errors.push('Could not read HTML file');
}

// Test 10: Check setup scripts
console.log('10. Checking setup scripts...');
if (fs.existsSync('setup-backend.sh') && fs.existsSync('setup-client.sh')) {
    console.log('    ✅ Setup scripts exist');
} else {
    warnings.push('Setup scripts not found');
}

// Summary
console.log('\n📊 Test Summary:');
console.log(`   Tests run: 10`);
console.log(`   Errors: ${errors.length}`);
console.log(`   Warnings: ${warnings.length}`);

if (errors.length > 0) {
    console.log('\n❌ Errors found:');
    errors.forEach(error => console.log(`   • ${error}`));
}

if (warnings.length > 0) {
    console.log('\n⚠️  Warnings:');
    warnings.forEach(warning => console.log(`   • ${warning}`));
}

if (errors.length === 0) {
    console.log('\n✅ All critical tests passed! The transformation appears successful.');
    console.log('\nNext steps:');
    console.log('1. Run ./setup-backend.sh to install backend dependencies');
    console.log('2. Configure backend/.env with your Supabase and OpenAI credentials');
    console.log('3. Set up your Supabase database using backend/supabase-schema.sql');
    console.log('4. Run ./setup-client.sh to install client dependencies');
    console.log('5. Start the backend: cd backend && npm run dev');
    console.log('6. Start the client: npm run dev');
} else {
    console.log('\n❌ Critical errors found. Please fix these issues before proceeding.');
    process.exit(1);
}
